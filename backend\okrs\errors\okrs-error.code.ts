import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@/common';

/**
 * Mã lỗi cho module OKRs
 * Phạm vi: 11000-11099
 */
export const OKRS_ERROR_CODES = {
  // Objective errors (11000-11019)
  OBJECTIVE_NOT_FOUND: new ErrorCode(
    11000,
    'Không tìm thấy mục tiêu',
    HttpStatus.NOT_FOUND,
  ),
  OBJECTIVE_INVALID_DATE_RANGE: new ErrorCode(
    11001,
    'Ng<PERSON>y bắt đầu phải trước ngày kết thúc',
    HttpStatus.BAD_REQUEST,
  ),
  OBJECTIVE_INVALID_CYCLE: new ErrorCode(
    11002,
    'Chu kỳ OKR không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  OBJECTIVE_INVALID_PARENT: new ErrorCode(
    11003,
    '<PERSON><PERSON><PERSON> tiê<PERSON> cha không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  OBJECTIVE_INVALID_OWNER: new ErrorCode(
    11004,
    '<PERSON>ời sở hữu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  OBJECTIVE_INVALID_DEPARTMENT: new ErrorCode(
    11005,
    'Phòng ban không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Key Result errors (11020-11039)
  KEY_RESULT_NOT_FOUND: new ErrorCode(
    11020,
    'Không tìm thấy kết quả chính',
    HttpStatus.NOT_FOUND,
  ),
  KEY_RESULT_INVALID_TARGET: new ErrorCode(
    11021,
    'Giá trị mục tiêu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_INVALID_OBJECTIVE: new ErrorCode(
    11022,
    'Mục tiêu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_INVALID_WEIGHT: new ErrorCode(
    11023,
    'Trọng số phải từ 0 đến 100',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_INVALID_PROGRESS: new ErrorCode(
    11024,
    'Tiến độ phải từ 0 đến 100',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_INVALID_VALUES: new ErrorCode(
    11025,
    'Giá trị kết quả chính không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Key Result Update errors (11040-11059)
  KEY_RESULT_UPDATE_NOT_FOUND: new ErrorCode(
    11040,
    'Không tìm thấy bản ghi cập nhật',
    HttpStatus.NOT_FOUND,
  ),
  KEY_RESULT_UPDATE_INVALID_VALUE: new ErrorCode(
    11041,
    'Giá trị cập nhật không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_UPDATE_INVALID_CONFIDENCE: new ErrorCode(
    11042,
    'Mức độ tự tin phải từ 1 đến 5',
    HttpStatus.BAD_REQUEST,
  ),

  // OKR Cycle errors (11060-11079)
  OKR_CYCLE_NOT_FOUND: new ErrorCode(
    11060,
    'Không tìm thấy chu kỳ OKR',
    HttpStatus.NOT_FOUND,
  ),
  OKR_CYCLE_INVALID_DATE_RANGE: new ErrorCode(
    11061,
    'Ngày bắt đầu phải trước ngày kết thúc',
    HttpStatus.BAD_REQUEST,
  ),
  OKR_CYCLE_OVERLAP: new ErrorCode(
    11062,
    'Chu kỳ OKR bị trùng lặp với chu kỳ khác',
    HttpStatus.BAD_REQUEST,
  ),
  OKR_CYCLE_ALREADY_CLOSED: new ErrorCode(
    11063,
    'Chu kỳ OKR đã đóng',
    HttpStatus.BAD_REQUEST,
  ),

  // Key Result Support errors (11080-11099)
  KEY_RESULT_SUPPORT_INVALID: new ErrorCode(
    11080,
    'Mối quan hệ hỗ trợ không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_SUPPORT_CYCLE: new ErrorCode(
    11081,
    'Phát hiện chu trình trong mối quan hệ hỗ trợ',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_SUPPORT_SELF: new ErrorCode(
    11082,
    'Kết quả chính không thể hỗ trợ chính nó',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_SUPPORT_SELF_REFERENCE: new ErrorCode(
    11083,
    'Kết quả chính không thể hỗ trợ chính nó',
    HttpStatus.BAD_REQUEST,
  ),
  KEY_RESULT_SUPPORT_NOT_FOUND: new ErrorCode(
    11084,
    'Không tìm thấy mối quan hệ hỗ trợ',
    HttpStatus.NOT_FOUND,
  ),
  KEY_RESULT_SUPPORT_ALREADY_EXISTS: new ErrorCode(
    11085,
    'Mối quan hệ hỗ trợ đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),
};
