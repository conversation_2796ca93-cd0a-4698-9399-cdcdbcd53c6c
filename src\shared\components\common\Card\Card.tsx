import React, { HTMLAttributes, ReactNode } from 'react';

export interface CardProps extends Omit<HTMLAttributes<HTMLDivElement>, 'title'> {
  /**
   * Nội dung của card
   */
  children: ReactNode;

  /**
   * Tiêu đề của card
   */
  title?: ReactNode;

  /**
   * Tiêu đề phụ của card
   */
  subtitle?: ReactNode;

  /**
   * Nội dung footer của card
   */
  footer?: ReactNode;

  /**
   * Không sử dụng padding
   */
  noPadding?: boolean;

  /**
   * Variant của card
   */
  variant?: 'default' | 'bordered' | 'elevated';

  /**
   * Hiệu ứng hover
   */
  hoverable?: boolean;

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Hiển thị đường viền màu theo variant
   */
  colorBorder?: 'primary' | 'success' | 'warning' | 'danger' | 'info';

  /**
   * Hiển thị header với màu nền
   */
  colorHeader?: 'primary' | 'success' | 'warning' | 'danger' | 'info';

  /**
   * Nội dung bên phải header
   */
  extra?: ReactNode;

  /**
   * Icon hiển thị trong header
   */
  icon?: ReactNode;

  /**
   * Header tùy chỉnh
   */
  customHeader?: ReactNode;

  /**
   * Cho phép nội dung tràn ra ngoài card
   */
  allowOverflow?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Card component hiển thị nội dung trong một container có viền
 *
 * @example
 * // Card cơ bản
 * <Card title="Card Title">
 *   <p>Card content</p>
 * </Card>
 *
 * @example
 * // Card với variant và hoverable
 * <Card
 *   title="Card Title"
 *   variant="elevated"
 *   hoverable
 *   footer={<Button>Action</Button>}
 * >
 *   <p>Card content</p>
 * </Card>
 */
const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  footer,
  noPadding = false,
  variant = 'default',
  hoverable = false,
  loading = false,
  colorBorder,
  colorHeader,
  extra,
  icon,
  customHeader,
  allowOverflow = false,
  className = '',
  ...rest
}) => {
  // Base classes
  const baseClasses = `card ${allowOverflow ? '' : 'overflow-hidden'} rounded-xl`;

  // Variant classes
  const variantClasses = {
    default: 'bg-white dark:bg-gray-800 shadow-sm',
    bordered: 'bg-white dark:bg-gray-800 shadow-sm', // Removed border, using shadow instead
    elevated: 'bg-white dark:bg-gray-800 shadow-md',
  };

  // Hover effect
  const hoverClass = hoverable ? 'transition-shadow duration-300 hover:shadow-lg' : '';

  // Color border
  const colorBorderClasses = colorBorder
    ? {
        primary: 'border-l-4 border-primary',
        success: 'border-l-4 border-green-500',
        warning: 'border-l-4 border-yellow-500',
        danger: 'border-l-4 border-red-500',
        info: 'border-l-4 border-blue-500',
      }[colorBorder]
    : '';

  // Padding class
  const paddingClass = noPadding ? '' : 'p-4';

  // Combine all classes
  const cardClasses = [
    baseClasses,
    variantClasses[variant],
    hoverClass,
    colorBorderClasses,
    paddingClass,
    className,
  ].join(' ');

  // Color header
  const headerBgClass = colorHeader
    ? {
        primary: 'bg-primary text-white',
        success: 'bg-green-500 text-white',
        warning: 'bg-yellow-500 text-white',
        danger: 'bg-red-500 text-white',
        info: 'bg-blue-500 text-white',
      }[colorHeader]
    : '';

  // Loading overlay
  const renderLoading = () => (
    <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-70 dark:bg-opacity-70 flex items-center justify-center z-10">
      <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
    </div>
  );

  return (
    <div className={`${cardClasses} relative`} {...rest}>
      {/* Loading overlay */}
      {loading && renderLoading()}

      {/* Card header */}
      {customHeader ? (
        <div className={`${noPadding ? 'p-4 pb-0' : 'mb-4'}`}>{customHeader}</div>
      ) : (
        (title || subtitle || extra || icon) && (
          <div className={`${noPadding ? 'p-4 pb-0' : 'mb-4'} ${headerBgClass}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {icon && <div className="mr-3">{icon}</div>}
                <div>
                  {title && <h3 className="text-lg font-semibold">{title}</h3>}
                  {subtitle && <p className="text-sm opacity-70 mt-1">{subtitle}</p>}
                </div>
              </div>
              {extra && <div>{extra}</div>}
            </div>
          </div>
        )
      )}

      {/* Card content */}
      <div className={noPadding ? '' : ''}>{children}</div>

      {/* Card footer */}
      {footer && <div className={`${noPadding ? 'p-4 pt-0' : 'mt-4 pt-4'}`}>{footer}</div>}
    </div>
  );
};

export default Card;
