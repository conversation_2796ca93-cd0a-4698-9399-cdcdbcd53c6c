import { get, isDate } from 'lodash';
import { useState, useCallback, useMemo } from 'react';

import { SortOrder } from '../types';

/**
 * Loại dữ liệu cho sắp xếp
 */
export type SortType = 'string' | 'number' | 'date' | 'boolean' | 'custom';

/**
 * Hàm so sánh tùy chỉnh
 */
export type CompareFn<T> = (a: T, b: T, sortOrder: SortOrder) => number;

/**
 * Cấu hình sắp xếp cho một cột
 */
export interface ColumnSortConfig<T> {
  /**
   * Loại dữ liệu
   * @default 'string'
   */
  type?: SortType;

  /**
   * Hàm so sánh tùy chỉnh
   */
  compareFn?: CompareFn<T>;

  /**
   * Hàm chuyển đổi giá trị trước khi so sánh
   */
  transformFn?: (value: unknown) => unknown;
}

interface UseTableSortOptions<T> {
  /**
   * D<PERSON> liệu ban đầu
   */
  data: T[];

  /**
   * C<PERSON><PERSON> sắp xếp mặc định
   */
  defaultSortColumn?: string | null;

  /**
   * Thứ tự sắp xếp mặc định
   */
  defaultSortOrder?: SortOrder;

  /**
   * Cấu hình sắp xếp cho các cột
   */
  columnSortConfig?: Record<string, ColumnSortConfig<T>>;

  /**
   * Callback khi thay đổi sắp xếp, dùng để gọi API
   */
  onSortChange?: (column: string | null, order: SortOrder) => void;
}

interface UseTableSortResult<T> {
  /**
   * Dữ liệu đã sắp xếp
   */
  sortedData: T[];

  /**
   * Cột đang sắp xếp
   */
  sortColumn: string | null;

  /**
   * Thứ tự sắp xếp
   */
  sortOrder: SortOrder;

  /**
   * Hàm thay đổi sắp xếp
   */
  handleSort: (column: string, order: SortOrder) => void;
}

/**
 * Hook xử lý sắp xếp dữ liệu trong bảng
 */
export function useTableSort<T>({
  data,
  defaultSortColumn = null,
  defaultSortOrder = null,
  columnSortConfig = {},
  onSortChange,
}: UseTableSortOptions<T>): UseTableSortResult<T> {
  // State lưu trữ cột đang sắp xếp và thứ tự sắp xếp
  const [sortColumn, setSortColumn] = useState<string | null>(defaultSortColumn);
  const [sortOrder, setSortOrder] = useState<SortOrder>(defaultSortOrder);

  // Hàm thay đổi sắp xếp
  const handleSort = useCallback(
    (column: string, order: SortOrder) => {
      const newSortColumn = order === null ? null : column;
      setSortColumn(newSortColumn);
      setSortOrder(order);

      // Gọi callback nếu có
      if (onSortChange) {
        onSortChange(newSortColumn, order);
      }
    },
    [onSortChange]
  );

  // Hàm so sánh giá trị dựa trên loại dữ liệu
  const compareValues = useCallback(
    (valueA: unknown, valueB: unknown, type: SortType, sortOrder: SortOrder): number => {
      // So sánh các giá trị
      if (valueA === valueB) {
        return 0;
      }

      // Xử lý các trường hợp null và undefined
      if (valueA === null || valueA === undefined) {
        return sortOrder === 'asc' ? -1 : 1;
      }
      if (valueB === null || valueB === undefined) {
        return sortOrder === 'asc' ? 1 : -1;
      }

      // So sánh dựa trên loại dữ liệu
      switch (type) {
        case 'string': {
          if (typeof valueA === 'string' && typeof valueB === 'string') {
            return sortOrder === 'asc'
              ? valueA.localeCompare(valueB)
              : valueB.localeCompare(valueA);
          }
          // Chuyển đổi sang string nếu không phải
          return sortOrder === 'asc'
            ? String(valueA).localeCompare(String(valueB))
            : String(valueB).localeCompare(String(valueA));
        }

        case 'number': {
          const numA = Number(valueA);
          const numB = Number(valueB);
          return sortOrder === 'asc' ? numA - numB : numB - numA;
        }

        case 'date': {
          const dateA = valueA instanceof Date ? valueA : new Date(String(valueA));
          const dateB = valueB instanceof Date ? valueB : new Date(String(valueB));
          return sortOrder === 'asc'
            ? dateA.getTime() - dateB.getTime()
            : dateB.getTime() - dateA.getTime();
        }

        case 'boolean': {
          const boolA = Boolean(valueA);
          const boolB = Boolean(valueB);
          if (boolA === boolB) {return 0;}
          return sortOrder === 'asc' ? (boolA ? 1 : -1) : boolA ? -1 : 1;
        }

        default:
          // Mặc định xử lý như string nếu là string, ngược lại xử lý như number
          if (typeof valueA === 'string' && typeof valueB === 'string') {
            return sortOrder === 'asc'
              ? valueA.localeCompare(valueB)
              : valueB.localeCompare(valueA);
          }
          return sortOrder === 'asc'
            ? Number(valueA) - Number(valueB)
            : Number(valueB) - Number(valueA);
      }
    },
    []
  );

  // Sắp xếp dữ liệu
  const sortedData = useMemo(() => {
    // Nếu không có cột sắp xếp hoặc không có thứ tự sắp xếp, trả về dữ liệu ban đầu
    if (!sortColumn || !sortOrder) {
      return data;
    }

    // Lấy cấu hình sắp xếp cho cột hiện tại
    const columnConfig = columnSortConfig[sortColumn] || {};
    const { type = 'string', compareFn, transformFn } = columnConfig;

    // Sắp xếp dữ liệu
    return [...data].sort((a, b) => {
      // Nếu có hàm so sánh tùy chỉnh, sử dụng nó
      if (compareFn) {
        return compareFn(a, b, sortOrder);
      }

      // Lấy giá trị từ đối tượng
      let valueA = get(a, sortColumn);
      let valueB = get(b, sortColumn);

      // Áp dụng hàm chuyển đổi nếu có
      if (transformFn) {
        valueA = transformFn(valueA);
        valueB = transformFn(valueB);
      }

      // Tự động phát hiện kiểu dữ liệu nếu không được chỉ định rõ
      if (type === 'custom') {
        // Phát hiện kiểu dữ liệu
        if (
          isDate(valueA) ||
          isDate(valueB) ||
          (typeof valueA === 'string' && !isNaN(Date.parse(valueA))) ||
          (typeof valueB === 'string' && !isNaN(Date.parse(valueB)))
        ) {
          return compareValues(valueA, valueB, 'date', sortOrder);
        } else if (typeof valueA === 'boolean' || typeof valueB === 'boolean') {
          return compareValues(valueA, valueB, 'boolean', sortOrder);
        } else if (
          typeof valueA === 'number' ||
          typeof valueB === 'number' ||
          !isNaN(Number(valueA)) ||
          !isNaN(Number(valueB))
        ) {
          return compareValues(valueA, valueB, 'number', sortOrder);
        } else {
          return compareValues(valueA, valueB, 'string', sortOrder);
        }
      }

      // So sánh các giá trị dựa trên loại dữ liệu đã chỉ định
      return compareValues(valueA, valueB, type, sortOrder);
    });
  }, [data, sortColumn, sortOrder, columnSortConfig, compareValues]);

  return {
    sortedData,
    sortColumn,
    sortOrder,
    handleSort,
  };
}
