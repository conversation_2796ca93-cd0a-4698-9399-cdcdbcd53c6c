import {
  BarChart3,
  BriefcaseBusiness,
  BusFront,
  FileText,
  FileVideo2,
  FolderOpen,
  MailPlus,
  Pickaxe,
  Store,
} from 'lucide-react';
import React from 'react';

export type IconName =
  | 'user'
  | 'language'
  | 'sun'
  | 'moon'
  | 'settings'
  | 'logout'
  | 'check'
  | 'star'
  | 'plus'
  | 'minus'
  | 'chevron-left'
  | 'chevron-right'
  | 'chevron-up'
  | 'chevron-down'
  | 'search'
  | 'paperclip'
  | 'send'
  | 'menu'
  | 'home'
  | 'animation'
  | 'components'
  | 'upload'
  | 'microphone'
  | 'close'
  | 'image'
  | 'pdf'
  | 'doc'
  | 'sheet'
  | 'presentation'
  | 'file'
  | 'warning'
  | 'info'
  | 'chat'
  | 'save'
  | 'link'
  | 'mail'
  | 'building'
  | 'document'
  | 'map-pin'
  | 'phone'
  | 'layout'
  | 'filter'
  | 'layers'
  | 'grid'
  | 'server'
  | 'list'
  | 'code'
  | 'folder'
  | 'loading'
  | 'calendar'
  | 'power'
  | 'edit'
  | 'eye'
  | 'eye-off'
  | 'lock'
  | 'help-circle'
  | 'alert-circle'
  | 'globe'
  | 'trash'
  | 'arrow-left'
  | 'rpoint'
  | 'shopping-cart'
  | 'copy'
  | 'x'
  | 'users'
  | 'campaign'
  | 'chart'
  | 'trending-up'
  | 'trending-down'
  | 'payment'
  | 'credit-card'
  | 'award'
  | 'workflow'
  | 'integration'
  | 'payment'
  | 'subscription'
  | 'toggle-on'
  | 'toggle-off'
  | 'filter-v2'
  | 'more-horizontal'
  | 'more-vertical'
  | 'business'
  | 'crawl'
  | 'file-media'
  | 'marketplace'
  | 'file-text'
  | 'box'
  | 'refresh-cw'
  | 'bar-chart'
  | 'package'
  | 'cloud'
  | 'tag'
  | 'facebook'
  | 'zap'
  | 'database'
  | 'website'
  | 'google'
  | 'sms'
  | 'message-square'
  | 'credit-card'
  | 'bank'
  | 'robot'
  | 'webhook'
  | 'tiktok'
  // Social media icons
  | 'instagram'
  | 'linkedin'
  | 'threads'
  | 'telegram'
  | 'function'
  | 'mail-plus'
  | 'truck'
  | 'dollar-sign'
  | 'project'
  | 'statistics'
  | 'reports'
  | 'folder-plus'
  | 'bar-chart-2'
  | string;

export interface IconProps {
  /**
   * Tên icon
   */
  name: IconName;

  /**
   * Kích thước của icon
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Màu sắc của icon
   */
  color?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Sử dụng fill thay vì stroke
   */
  fill?: boolean;
}

/**
 * Component hiển thị các icon SVG
 */
const Icon: React.FC<IconProps> = ({ name, size = 'md', color, className = '', fill = false }) => {
  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8',
  }[size];

  // Xác định style cho màu sắc
  const colorStyle = color ? { color } : {};

  // Xác định class chung cho tất cả các icon
  const iconClasses = `${sizeClasses} ${fill ? 'fill-current' : 'stroke-current'} ${className}`;

  // Render icon dựa trên tên
  const renderIcon = () => {
    switch (name) {
      case 'truck':
        return <BusFront className={iconClasses} style={colorStyle} />;

      case 'mail-plus':
        return <MailPlus className={iconClasses} style={colorStyle} />;

      case 'marketplace':
        return <Store className={iconClasses} style={colorStyle} />;

      case 'website':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
            />
          </svg>
        );
      case 'business':
        return <BriefcaseBusiness className={iconClasses} style={colorStyle} />;
      case 'crawl':
        return <Pickaxe />;
      case 'copy':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
        );

      case 'power':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        );
      case 'credit-card':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={iconClasses}
            style={colorStyle}
          >
            <path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
            <path d="m9 8 3 3v7" />
            <path d="m12 11 3-3" />
            <path d="M9 12h6" />
            <path d="M9 16h6" />
          </svg>
        );
      case 'edit':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
        );
      case 'eye':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
          </svg>
        );
      case 'user':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        );
      case 'language':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
            />
          </svg>
        );
      case 'logout':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
        );

      case 'sun':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        );
      case 'moon':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
            />
          </svg>
        );
      case 'settings':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        );

      case 'check':
        return (
          <svg className={iconClasses} style={colorStyle} viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
          </svg>
        );
      case 'star':
        return (
          <svg className={iconClasses} style={colorStyle} viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
          </svg>
        );
      case 'plus':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        );
      case 'minus':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 12h12" />
          </svg>
        );
      case 'chevron-left':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        );
      case 'chevron-right':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        );
      case 'chevron-up':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        );
      case 'chevron-down':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        );
      case 'search':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        );
      case 'paperclip':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
            />
          </svg>
        );
      case 'send':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
            />
          </svg>
        );
      case 'menu':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        );
      case 'home':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
        );
      case 'animation':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z"
            />
          </svg>
        );
      case 'components':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
            />
          </svg>
        );
      case 'upload':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
            />
          </svg>
        );
      case 'microphone':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
            />
          </svg>
        );
      case 'close':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        );
      case 'image':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      case 'pdf':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
            <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor" stroke="none">
              PDF
            </text>
          </svg>
        );
      case 'doc':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
            <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor" stroke="none">
              DOC
            </text>
          </svg>
        );
      case 'sheet':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
            <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor" stroke="none">
              XLS
            </text>
          </svg>
        );
      case 'presentation':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
            <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor" stroke="none">
              PPT
            </text>
          </svg>
        );
      case 'file':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
        );
      case 'warning':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        );
      case 'info':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case 'chat':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        );
      case 'save':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
            />
          </svg>
        );
      case 'link':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
        );
      case 'mail':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
        );
      case 'building':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        );
      case 'document':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        );
      case 'map-pin':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
            />
          </svg>
        );
      case 'phone':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        );
      case 'layout':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
            />
          </svg>
        );
      case 'filter':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
            />
          </svg>
        );
      case 'filter-v2':
        return (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-list-filter-icon lucide-list-filter"
          >
            <path d="M3 6h18" />
            <path d="M7 12h10" />
            <path d="M10 18h4" />
          </svg>
        );
      case 'more-horizontal':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="1" />
            <circle cx="19" cy="12" r="1" />
            <circle cx="5" cy="12" r="1" />
          </svg>
        );
      case 'more-vertical':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="1" />
            <circle cx="12" cy="5" r="1" />
            <circle cx="12" cy="19" r="1" />
          </svg>
        );
      case 'layers':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
        );
      case 'grid':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            />
          </svg>
        );
      case 'server':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"
            />
          </svg>
        );
      case 'list':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 0
              0-2 2h2a2 2 0 002-2M9 5a2 2 0 00-2 2h2a2 2 0 002-2"
            />
          </svg>
        );
      case 'code':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
            />
          </svg>
        );
      case 'loading':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        );

      case 'calendar':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      case 'shopping-cart':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
        );
      case 'workflow':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
            />
          </svg>
        );
      case 'integration':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      case 'subscription':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 9a2 2 0 10-4 0v5a2 2 0 01-2 2h6m-6-4h4m8 0a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case 'toggle-on':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17 7H7a5 5 0 0 0 0 10h10a5 5 0 0 0 0-10zm0 8a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"
              fill="currentColor"
              stroke="none"
            />
          </svg>
        );
      case 'toggle-off':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M17 7H7a5 5 0 0 0 0 10h10a5 5 0 0 0 0-10zM7 15a3 3 0 1 1 0-6 3 3 0 0 1 0 6z"
              fill="currentColor"
              stroke="none"
            />
          </svg>
        );
      case 'rpoint':
        return (
          <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
          >
            <path
              d="M0 12.5C0 5.59644 5.59644 0 12.5 0C19.4036 0 25 5.59644 25 12.5C25 19.4036 19.4036 25 12.5 25C5.59644 25 0 19.4036 0 12.5Z"
              fill="url(#pattern0_11_1136)"
            />
            <defs>
              <pattern
                id="pattern0_11_1136"
                patternContentUnits="objectBoundingBox"
                width="1"
                height="1"
              >
                <use xlinkHref="#image0_11_1136" transform="scale(0.000781861)" />
              </pattern>
              <image
                id="image0_11_1136"
                width="1279"
                height="1279"
                preserveAspectRatio="none"
                xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABP8AAAT/CAMAAABNQKdpAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABFFBMVEX////tLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDntLDnxYGrvRlHwU13zbXb2lZz6ys395eb819r5vcH1iI/uOEX4sLT+8vP0e4P3oqjtLDnsMT/tO0jsLDrze4P////719ruRlL4t7zybnjuP0zsMj/tOEXtOUbtNkP2oqnzcnrvTlrtM0HuQU7tOEbvU17tNEHxZ3HuR1P4sLX95eb95uf1kJfwXmnuQU30iJD5vcH+8vP7ztDzfofwVmHwXWj1lZz6ys7tOkftNUPybXf3oqjtMj/vSlbuPUruQEzxYGvuQE3xZG7uP03xaXPtPEntN0TuPEntO0ftMkDtM0DuQk81sOz9AAAAIHRSTlMAEEAwcICgwOBQIGDQsPCQz5u1p41mMBYkPnLDSgiBWE7+uw4AAAABYktHRACIBR1IAAAACXBIWXMAAAsTAAALEwEAmpwYAACAAElEQVR42u29d58rX3Kfh8l5bgMzaIRBGO/d30qyl6ZJSpZIBXIlBjGYwUGi5ff/PvxBOqED0KeqTur+Pn/t3t/FXAxm8KDqVDijEQBhubpu5ib2EwMAABH2lru9u7u7u3/Y892dx8Mjdg++e9p9ndjfDAAAnOX5+vrl7u7u4eHh1cF1nXl4eLi/u7u7vr6+iv2dAgDAaDS6uX7aR3iPPpTXzusuOryFCgEAwbm6vr67e/MT5zny/vDw4+4Fh4cAAM88X7/c3SehvQZ2ISGOCgEA0lw/3f0IneMyPHiLeBAAwOb66e7+4T220ig8Pvy4u36O/foBADLk+fo2m4jvHK9vdy9IigEA3Xi+vrt36dbrSKEYNzPRf0P8H398gAUBAOe42sV8Uq77GI/Hn9Md5Z5Z9+cx3z9gsXvscmfGLyEnPj7cPeFgEABQ4fnpjpXtHuO65XS6KstyLf8EZ2VZTqfTzXj8wZTh6/0t+gYBAHtuXshB3156m+l0UZZBn/KsLFfTKUeFj293kCAAg4aovqKY7FJbp5zWF/NytUuSSSKEBAEYJs9P7urbHeotp4sUtFdnXm6nn7ujQoIEYz93AEAwru/enFr6iuJr/Dld+TjWk2fnwV1i7CTB1/sXFEYA6DvPLz9cxteKYryZLsIe7skwc9bg+wMCQQB6y83tW+cS7+6Ib7rNI+I7x7ycbsYOFnz98YSZEQB6xvVd19O+vflWOYZ87cycLPh4/wIHAtATOruvKMbLbb/MZzIrp59dM+LHt1scCAKQOV3dV0w+p4sk67rSlKtlx1DwHXEgANlyc9vFfUXxsZnmf9DnxmwxHX91kSByYQDy4/m2S4tLMfnscb57ia4SfP3xhC5pADLh6un+cp23+PocXNTXxKxbOvxwh+NAAJLn5vKBX1F8LFeDOOvrSrn9nFx0II4DAUiZq5f7S0nvPuyL/TyTZLboEAi+/kCLNAAJcnN7abCjmGxW89hPM23KyyeC7/cvOA0EICUunvgVH8thtLfwmW8/LznwFc2BAKTB1cvbZfeh0uHEZQc+IhMGIDbPF7LeYgL3EbnowPd79MUAEI2b87tcdud9yHlZzLcXaiJvOAwEIAI3P84d+RVfn1vUOkQolx9nFfhwi7YYAEJyfV5+H0v0uEiyXm3OpsKvUCAAgXg61+VXfG1WOPDzwC4VhgIBiMrNj3Py+5gi6fXHerE5NyYCBQLgk3NnfsXXJwI//8yn504DX1EOAcALz+fkN1kuYj+/wTBbfZ4pCqMiDIA05/r8ig+UekOzOFcQuX+K/fQA6A/nJjyKzy16/KIwX7YfBr7fY0AOAAme7lvdV+DILyrzbfth4OMdqiEA8GiveBRfGxz5xWd2RoGohgBA56r10A/yS4hzCsRRIAAkWvNeyC85zigQeTAArrQ3u0B+aXJGgQ8vsZ8cABnx0naHBwoeKTPbtlWEUQ8GoBvPbRNuxRjyS532phgUQwC4SFvoV3ygzy8PyrbWaASBAJyjLfQrJktMeGTE4hNBIAButIZ+qHhkx7qtGvJ+j3IwAFWe75oLvjj0y5VZ21EgysEAWFw39/oVkykO/TJmsWk2IHoCAThx9dIS+iHvzZ7WPPge12cC0FrzKCZb5L29YL75aqmFxH5mAETmunmz1dcG9d7+sF41Xx3yjjQYDJmXxv0GxUefSh6zcsf0wGZ8gePfW+0f1KdXYdncFIiWQDBQrporvrmHfrOD7Zbj8fij2PFNZvfor6MVt2WZ+Z2eLUHgAzbEgOHx3HiJZTHJM/Qry9U+uPti+a6DDj/G4+V0WpZZfka0nAQ+oikaDIvmY79ik1eIM99Zbzye+JReuws/xp87E2b1ebFeNfYEvt/BgGAwNA56ZNTrt0tw97FeaOs1m3Ay3kxXZTYvXnNPIMZCwDBo7PYrxqvYz+sys3I7/RxHiPa6e3CRQQQ9mzbWQtARCHrP1V3jsV/iNY91uZruqhmxHddJg187Dab9eo5WjV3RDzAg6DON9ism03TPsGbldDPOQnxVDU7G023Ch4PNaTB6okFveW4a8k028c3VfPar+zVebhNNiWfTpmrwIwwI+kiz/ZKs+JbbpYz59v1/+77mzbGxuTzP4vjX9o/htg/qZzEZJ5kRN1eDH29RDAY9o9F+X8vUipbrcvo5YTjn6Lud7RYyAxzzct9buDMir8+mKFIMBRdNTdFohwG94rqh4SW1Y7/ZYjr+Ivml2AVYh7Zk335Z72X4OaYWoYviY5OYBOcbGBD0mWb7JXTst4v63NW3D/WWu8G0KE96tutC/BwTotW9BBNKh2fLhoNAGBD0gkb7jZOJQebbzcTNILsOk89o2quzLhfTpXN3zi4dXqRy/LBu6giEAUH2NNrvMw11rBdTpzLHLs1dTpPxXu3bKVfTjdtUSjH5TCUbbiqFwIAgaxrtt0kh6pitHMK+XcS3nOayc6Bc7IdUHALB6SKFk9imnmgYEGTLTYP9vhKw33zb+bRv3zqySiREcmK2a+LpfKhZfCwTcGA5hgFBT2jqePmKXvLt7L6D+TIJ+VpZO7QyFpPNKvaHEwwIekGK9uvqvjQ75RjMyulnt/pIMYkdBzbNxb1jJgTkRHr2m602XdxXfKU5KSFBue02z7fLhWM+z1mDATEVB7KhactBTPutF8sOtY69+mLnf96Zr5YdIsFdTSTipwAMCLKlwX4xt9rPp+NOb/f+q09Tbjcfl1+Ur4jHgU0GxHYskD4vDbFfrFGPdYekt/jYZF/lIL02ZYdxv+JjGescFAYE+fFU3+0cK/OdTy/FOLuwL+ENeSFeo9XFQLD4+owUvDcZEFvyQbo0tDvHsd96cam7ufj6TGkENiIdpmCKjzingU0G/IFmGJAkz/U73aLYb7b6vPB2ngwz5T1DecmBxWQToyg8qx/eoh0QJMhVveUlhv3m249L7+PoTb6JcsmBcTLhho5olIJBYjQVfZfB3yzz830uuzcw3HeWiw6M8Ao2GPAVhRCQEPUrLcNvOSjPyq8ocN7XkcXybAhdfGyD/2jrP9k3FEJAItTLHsVn4LfI4myjS8Q2jjy5MC0TXoEN27FQCAEpUJ91C73d9Kz8dm28g+5xoTLfjlNS4La2I/r9NvZLBAZP/eCvmAS13/ys/CI1bvSE821EgRW4rl+X+YhjQBCV+sFf0Hs9zhU8EPiJcLagXnyEfIkb2gEfcAwIolFfcBqy5WU2PSO/CU78xFivzuwNK0I2xTS0A6IbEMSh3vFXbIK9FdZnwpIIFcrec26FzlfA1uiy9mPHckAQg9vawd84mHXOTHgEjUcGxby9L6b4WgY7Z11NaknwTeyXBgyN69da0BUq4WyveODIzzOz9qg7XNDdUAhBLwwIST31DbXjaradtMsv6trigXBOgZ+BfgnWGyTBIB711DdQ2aM174X8AtKuwGB58LxWCEESDMJQT33DTHvMly15L+QXnDMKDNQSs6gdAyIJBv65+hHl4G+9an3DQX5ROKPATZDfiNoxIJJg4JvaevuvbYB/db75anmrodobkdYOzGKyDfBjmdVOQ9AODXzyXG14DtHx1xr6hZ0+AE20TuAECQLr3YB3sV8Q0F/uqr/jY/+n3W2hXzGZosk5CRatP6EAQWBtLQJmgoEfrquzvgF6XlbNO0iKrw32GqTDuuXH9B3gx1TvhblHHQSIU2v587/feTZtLvgG6zMDnWnryyzG3n9WtSQYdRAgzdN79ffa9yd72dzrV0ww3JsmLScVxZf3g4paEow6CJCkVvfwnfquV83hBPLelGnLg73XQmpJ8DvqIECM6ryH76rvbNocSaDemzyz5ib14sPzB2btipBXzIMAEarBn++G53LT+A76WiLvzYLmIcVi4nlGsvaZiRAQCFBtevmaev3nmlOoAMfoQIyWvmjPdwLWlqPilkzA5ea1KiKfv8Pr5ioiQr/saPkY+/SaOiyqISBGggGLWvDnc9a2+dgPp355Mlu2/DQ9/pvrZcW66IYGdGrBn8+Wv1nzsV+QWXrgg+bBxWLi8/OsVgdBCAiIVIM/nxdbNnb7eT8zB55prmV5vSVrihAQCFAL/jz+0pZNp0WoefSB5iGeL4+lkNpuVISAwJlK8Fd8+Gs8bk6T0OncF1p+vv4MWJ0HQQgI3KgHf97+qaZJjyLkPcLAO41psMdPuForDHoBgQOVgQ+PTS+N9psg8e0bjdXgYuztRLkaAmIcBHSlOvDhbcXzuqlP1uObAkSksbfT3w97/YkQEFB4CRT8rZva/TzPCMSh3LGaHvgct7I5/pX93+/h8WfTQaA/A1a7obEUBlzm6i1M8NdoP/+rkoIwK8vF0XRfxY5vAvsHfozH4+V0ui3LfgTFTYV+bwashoDvt7G/fZA612GCvyb7Zd7tNyvL6XQ5Hk+Ivuugw4/xeO/CnF+m+SagAash4Bs6YcA5Kpdbegr+mu2XZ9FjXq521vMjvVYXTnYmXOQpwllAA64r8eY7OmFAK5Wul+LDT/C3mtR//33vhxNnXm6nn7v0Npz2mkW4yc+DjZ9/ngxYLQSjGRq0cFv5jfTT89fQ8ZJVyXdeTpe7JDem9+oeHI+n24w02GxALx+31V5AdMKAJq7srhdPAx8Z229dbqepia+mwc/pIo/CcVPvk6fifzUERBkE1KgWPpY+/pH6fdVZ2G9dbpfjr4TFV7HgZDxdZWDBVSgDziu/diiDgAqVwoeXVS8NzQ/J22++muZjPtuCH5/TMvF2oiYDemkCmKIMAtp5rhQ+fNxvNMvMfutyu/lIOdvtZMGv8TLpULDBgF5mv8tKzQ3TIEBRmfjwseS5oekhXfuty+nnJHPzmS90kbIEGwzooxWqekXmK6ZBwJ6r+4qW5JOmhnJfqvbbR32xjeVJgos00+G6AYuJh4/gSjP0+1Ps7xukQLXpz0PL8zYP+80Wy3HuCe95iq/PaYIv/GgV5BekegLzI/a3DeJTyX0n8nnSov7xnp795ts+ZbznKIqP5Sq1QLApQfBQCp4iBwYm1dxXvvBRL/r6vj/d/SlOex721Skmm21aJ4JNBpQvhMztMghy4GFTyX3lCx/1skdac77r3qe87RRfn0k5cD2tl4LFD2OqZRDkwAPGzn3lx30bPtNTsl+57GWlw4W0HNj0aSmeK1TOGpEDD5Vq7is+7lvfc5DOtR4DzHnbKL42yZwHzj7rZ8XSz21mf+ohBx4mlZ7nL+kP2oaDv0TsN9t+ZjnS4ZFislyk8cNp+LVZSj+zJXLgwfNk575j4d+xdT2VSWKz/XqxGUid15liPE0iFa73C3xJH5pUWgEfMA88NOx5X/Hct3bwV4wTeG/Nt2O47xy7VDiBMLDWDijeMlDNgTEPPCgqu66kc9+y+gnu4RjbGQR+3Sg+4ldE6oUz8c6sSg6MnVgD4sZr7ls/wxbPX5yf0uoT1Y7u7E4DY//Eaucn0r0wlRwYO7EGw4v9yy6c+9Y/uiOXPebbwbe5uFN8fUbOhGuFEOkkuJIDYy/0MKi0vQjnvvXU9zNq2WO+RNZLpfjcRv3Z1Qoh0knwEo0wg8Nueyk+RH+jaqlv3Fm3xQZ9LjyKj6gKrOUSwklwJQfGUsDeY6+5F95yX9vzEvPgD/KTIaoCa8eAwtfSzD+sr45DwJ5jX/EmO+9bu9xDvnG1M5CfJDEVWD8GFP2tqvSp4hCw19hHf6Ifpetl9ffUz3WGHYD85ImowFo3oOzH9gqHgAPhyj76Ez1MXlR/SX1s8O0CCh6+KD62ceJ5zx+tcxwCDoJK15/kSXK97hGn52UG+XmliNQUM68mwaJ1kLX91e9xCNhH7K4/0baXat0jTuq7Rp9fAL42UQL7ahIsWwexA0ysxOohP+zfHkFBVW+XjpP6rj4hvzAUk2WEAbl6EiyZYth6fUcVpGdcvVm/OhvBLz1NIPWdo+IRlChHgbUkWHKovLIY/yX4dwc8Uml6FuzKqza9RNjzst7i0C88xWf4KL92ziLYClM5BMROwB5hVz6+5AxVS0rkL2y4xAJ5bySKyTL0Me/602MIaP8uoxW6N9iVD8Gjv7Ky4N7D9XHnmU0R+sWkGIce8Kn9yn3K/crZh4CogvQEu/Ihp6ha8Bd6yR9Cv/gUX4GDwNotcYLd0HNUQXqHve5FcNlV7ZM4bN0DoV8qhA4Cq3UQwRBwbZ9mowqSPfbMh9xnZTX4E55LvwRCv5QIHQRW6yCCIaA9DoxZkMyxKx8TMUdVg7+gdQ8UfNMjbDl4Vg0B5U51tvYsSMBvCohjXfImuOtvWm16CfjxP99AfilSTEL2BFbnzeXOnu2v/IoycL5YhV+5pufqwEfI4G+FG9zS5SvgFafVVhi5XkC7FRobsbLFLvyKSaoa/IVbcL9GzSNxinG4NLgWAkqd7thVEJSBM8Uq/IodEVePXmSXsZ39l5H4ZkAxCbYiphYCijU3WL9p7ygDZ0il8Cv12VgpvUm2n56nROKbC1/TUBnBwi7Dya31mKIPJm/siV+pykf1EzdY8LdC4psTxSZQN1TtF1LqlGeFMnDOWH0vYkFaGSf4w7FffhTjQMNAC0+/k/YsCASYFdYtb2KF32WUsu+sdgkiyIFiEmYspBYCConXvhoOfTAZYfe9CP0aVrpeAvX8oeiRL6FKIdUQUOhSV7sMDAFmg6U/qSt4K4WPMMFfCfvlzVeQofBKCCg2i7lBH0yG2H0vMr8Kvn7DzoKSbw8IY0BPn85LCDA7LP0Jeapa+JDbInPm34T9+kGQqZBZ5XRGqAyyQiNgXlw9mL8GQn0vlYkPuTUK7cB+PaIIYUA/v6T22SIEmDhW17PQVozKzQghljwvYL9+EcKAlSuMhJKUOQSYDxX9iXzNSnUtQMszmp17SAADrjeVDgWRD2q7DwaNgAljdz3LnAEvQ3e9IPPtKQEMWP2sFmkFXEOAeWDrT6TvpXqs7L3wAfv1GP8GrGznkPl9tQ+AIMBEsfQn0/ZXqft6L3zAfj1H8sbeZqY+cuANOqGTx9r1LNP2N/XSU9AK7DcAvPcDVsogMu8ECDB1XsR/6JW6r9QgSRszXGk0DHwbsNqrL3IOvoUAk8bSn8gaNLvwL9VJ3QbmfAeE749SexpEJm+xOqFxN3pi2PqT+HlvQzb91a5SB73G924Yu2dF5rN7hVm4ZLm1ftoCqqrkEH6b/tbYcDU4PO8HrLQCigScVkIEASaEOfIrEqnZn59ya8UbWU0uvltA//DcSrqyc2CJnVgQYJrY+hP4gnYbqdzFmU2UGPYYKn7bAauf4RJhAQSYIOL6m4ar+87R8jJkvJaCqzmwwCHgzHQqBJgE0vqrtL34rPuuUfQdOl4/XSs1PIF/ag0BJsa98E/Ybh/1WvdF2QN8Fx8eCyHVX2b+V7SHgbEOJjbS+qscG3vccr/AwR/43vfn+TsGtJMZiUNACDAlpPU3FT8yaQEHf0BReDwGFP+Ntq9FggBjIqw/u+tPaHa88R/CwR8w8dgPXWlmEPiHNhBgGgjrr9IxIHSNYANbHPwBm2LsLdeYif9aQ4BJYOpPYELDXnblrzBXfiD4AzX8ldrsbEMirYEAE8DSH//TcxWm7QWpL2jB353SW+nfbQgwOsL6sz8jva36Q+oLWvHXC1PJbfj/DAQYGVn9VfoEfK25R+oLzuJtP3TlEJB/uAMBRkVWf3blw9fRH1JfcJEwv3wCrdAQYESE9WdlB75u+Vgh9QWX8bYXZitcBYEAoyGrv5V0j3wTMzQ8g274On6xOwH5S90gwEjI6m8pnBg0MoX9QFeKiZ86iD0OzH/nmALEMoRgiOqvci7i5/QFS/6AE56aAe0yH/+oEQKMgKz+7MqHl89d3O4BnPF05YL9ac9uOIQAgyOqP7vw66fpeYG6B3DHUx1kJXvaAwEG5s748fH1Z6rJT+VjjWt9AQ0/8yBWKzS/0R8CDIp50SVbf1ZHip/KB5peABk/IaBdBWF/6EOAAZHVn+xZSANoegEsvISA9pk3u93VFODjlYfnC06Y+iu4tYqNbC2sAQz7AiaFjzNpu+eBHUeYX+0VAvSHpT+usGR/BxpA8AcE8NINvRT97IcAg3DzbvxWMH9m9hZvHxecI/gDIhQ+fjtXol2vG+OLQYCekNWf9QsmX/hdI/gDUvg4BbTLwMyl0Nbb6UH+yQJZ/VklMB+FX/T8AUF8FILnks0PlgDv5d9P4EpSf5KffQ2g5w8I4yEEtHMg5sAdBOiXq1fjZ8U0lh36yxd+SwR/QBoPK8ntMxrmIZAlwB/i76mBY+mPGatbR7/yY5aY9gVe8DARvJEUoHmmhG1Ysjzol1ZWf+J9L3Z3PQBieFiObwuQ92awTpUgQEmMnQdc/VlbcOVXPWPPH/CH/O+rZDRgCfBa+pkOGHPlywfvS0lG/HXQ8gy8It8M7UuAGAUW49b4CTGVZepPvu0PXS/AM/KdMFa1jinAErsQ5DGn3iT1J932h+vdQADEyyBzwXaIFXYhSHNt/uxZH35rr/qzV6kC4Anx3fj2NICcADEJJ4A59sGLzu1+T+mu5y2CPxAI6Z0w9juDJ0CzAIhJODbPnvQn3PWMiQ8QEOlpEMn3hplkYRCEidX3zDr48Ko/NP2BoEhPg1ijIIICxCAIC0t/rJ+KV/2h6Q+ERroVUPBs3HyroQ+ag9n3zAr5Lf0J33KJVVcgAtIXNsgJ0HqzPck+y0EhNvZh60/2kxO5L4iCdA4sKED0QUtgNv6xfh4zj/pD3RfEQrgOvBQToDUIgi4YGk/mT5rzUTcX7HCvgLoviIjwrV0rOQGiDZCL2fgnpz/ZY2PkviAqwr3QcgJcoQ2Qh7nvmTX2YelPduQXl5uD2MhejuRHgGgDdMbsfGGlrB71h3lfEB/ZbgY5AZrvjlvJpzgI3owfA+eQw5/+7Bs0AYiE7E4sOQF+Gs8RXTBu/DB+CGnqD5d8gESQbYQRE6DZdIYuGCdehH4E/vSHtheQDqKNMHICNKqD6IJxwFx59cn4Omtf+sOqP5AUoksBzbKeWPiBLpjOSHW+WFMfovrDqj+QFqKHgHMpAZYoArtjlX5T1N8cR38gNUQPAcUEaObSd3LPr9cYd11yOl+86W+F3BckiGQnoJgAzZk67ILpgln6ZZxqeNMfjv5Amkh2AloC5HRgGF0wKAJ3wCz9Mjb8+NIfll2BZJHca7kSWohqdcGgBnKJG+NVZ8TdvvSHS45Awkhe6iUmQLMILPf0+ok59cu56Xxs/FII6g9NzyBpirHcL7uUAM1UGkXgs5il3wnjJ2me0QnqD5UPkDqCrdBSAlyhBtIRY+Ezp/TrSX+ofID0EVxwaQmQUVwx78e5Fnt2/ePWeLkZpV8/+sOqU5AFglUQU4Acr5pF4GexZ9c3jLE3TunXbDlibQ60wMwHyIRiKfaWFBKg+ebBIFwL5lXnjDqW1GeWDWY+QDYIloGF3k0z1EAuYdY+GFmrH/0toD+QD4Xcsc9U5jCpxDbUC5i1D3rW6kd/KPyCvJArAwudpq9QAzmLWfug/+zMNFVOf0voD2SG3G+/kACNL4M5kBrm3Ae9fmVNLYqtREPfC8gPubsxzd9/xjpOswYi9dT6gjH3wTi8nUkNbZvgng+QJXJ9MEb7CuPduUYNpBVj5xV97M2ssgvqL/bvMQAkxPpgrDcW/YvOMQfSgrHzir7x1PopMfoHLXDDOcgWsT4YocjCqIFgF5bBk/Hq0o9tZaJ0G7T9gYwRWwotJMCN/iqPqIGcMO77YMRtximtmP7Q9gfyRqoR0KoskmMUU6NvQu/R7DEbn+nimsoUqSzQ9gdyx4cA6b01ZoUSbdAH7iV+WCsPOw+gP5A/Uo2AZvWCfkhfog26wovEj2rhQX/oegZ9QEqAMiHGFG3QFubhH7lj2QrOhfSHrmfQD3wIcEz+KkaZ8kHmeeWMcfhH7yyaeZh6g/5AX5Bqhl1KlBjXRkcZrgR+0y8G+SPFKs7L6A/LTkGPkBKg2WMxpX6ROY4AFcbWA3reOhb/QWPoA/QKKQGKvNVWOAI8Yh7+ke8XMD+UoD8AGvDwzqCnWkYb9KCPAM3DP3I4vRXve4b+QO8QGgY2T+/IR+3mG2zIR4BG5x/58M8sSkF/ALQgFByYrRbkLhjziwz3CNDo/CNf9ivy47C/IvQH+oiUACW6YHAEaB/+kSNpQ3+cC9M12HgAeoqQAFcSB07GEeBQB4EFDv/MVFWm8Q/6A71FSIDGCAd5X4n5zh3mILCx848cRpsrrxj302ugP9BjhARodlxQZ7bMd9oQdwEaO//InX9L6c4X6A/0GiEBSqRdRho9wCvRrwQ6/8yDCJHqPvQHeo6MAM3slXzsbhwBDu86EH3hB/nwz7SVyE8V+gO9R0aAVtsF8WuYrYRPEk8qI4y5N+rh31q68wX6AwNARoClQBHY6KR5f5Z4Utlg3Pb7NSN+DfMMAvoDoCMyAlwJnL0bo1uDmoMz596o9SOjBiXS+QL9gYEgI0Cz+Eg9wTfWKQxpDs5ofaHWLbbCpV/oDwwGGQEazWfUJG49yCYYo/WFenBXSiwiM4D+wIAQEaBZBBZ4Hw+mCeaKP/c2Ey79Qn9gUIgIUKIBw8iifwg8pRzQK5+p0zMSnzwmM+gPDAsRAZYCg3DGW3kYTTACrS9G5yS5fGyAhVdgcIgIcMuvgRhB5CA2wTzr7JfatmK+6gKlX+gPDBARAZpdGMRIxHg3D2ETjDH4QWx9KWVLv9AfGCQifRMCJ1FGE8yLwDNKGyP7JX7+GDVziY8w6A8MFJHogV8DMb5E78dAjMEP6ty0+YnDf0LQHxgsEgKc87OxxXDGQIzBD+J5qVEwlxh72zj8ugDQLyQEuOKfxhtvwn7vQr3TLxWxa1ng1TbZ4JpzMGAktgYb7yFiTmdsgul1Bmxkv8TU1aiWS3x2QX9g2EjMzhtHSMSOtnIYGbCR/dJedvO0TqD2MYX+wMAREKAxPkBN64xTrf5mwEb2S+wWN0auBeY+VtAfGDwCEwQlvw3aCGz6ugjByH6JYfKW321pAP0BIBJJbNlFSaOM/Mp+Ommis1+ivOYC105pSugPABkBmokZ7SsYZ1H9XAXIzn6NKpHAziusfAHgAPn+Wf3e1Pkr9SoyIwPuYw2Yn/1+Sv7AoD8AjgjMUZmNGbTcbN7vGjA7+92y24wMMPYBgEJAgCv22fy0zzVgdvY7l2x8hv4AMBBopjWGOIhHgPpN2bsuaHb2ax7+UVctaj47/2IAMAT4AjSPAGnH8z3OgNnZr2GsT/azwdgHADb8PmizDZrWBdjbDFhvvSIGb6KHf1voD4AKfAEu2F2APc2AjZ3PtOzXrC6xf0zoewagDj+wMKbYiO/zfu6C1jufadmvebTAPvybQ38ANMDvg2a/T40MuD+3Ib1wX5Wl/hmxD//Q+AdAM+wumBk7TzMy4L7chmTc90srjC8ED/+MOjIAwIQ6uqFZcd+q8/7dB2zc90v6TFiz60rG10LjHwBt8LtgjC5AWjRp5HrX3CeTBNf61aW1BY25X8AA6+4BaEcywKDNwRkJ2iP33Z4CV4/q+6Flv1tuUckAC08BOAe7C2bOnYMr+7UIxhh8I322zAXvO0LnCwDnYZ+ws+MVI0fLvwnQGHwjHa6y42kDdL4AcAn2pbJ6VIt2XmWc9+c/Bqdb/2gfLMZxKLc4NUPnCwCXYO+CWXObYIx+jxfmc4mNMfhGit6MwwBudyZKvwB0gD1iwH7T6opn5k2ARusfqXGZ/VFigNIvAF1gHzQZc3CkpM3I1O6ZTyUu9+r7oNWCjKME7ocSSr8AdINdBDYO7UlFT+PNmnMToNH6R9LXQq71BaVfALrCPWsyhkxpx/5aoDnfBqe3/pGKSkYYzG19wdQvAN3hhhtGEwzp4KvswyZAbuufMfjBPJHA1C8ADrAngbnvXd33ke0mQGPrH+nVND5DuCX5scOPHgDAnQReM3M34/G5bgJ8470CM+4ZgmaJwz8AnODWQBbMDHiVewnEKH6QPkuMCJo5lI3aBwCucIMOo3eDlAHr93+eexD03gPSYarOfrlnEah9AOAOswZinLmTmt/mee9BMCY/KJG0kf0yBxJR+wCAADfuKJnda0YJJL8pEGPygzQEbWS/zIMI1D4AoMDtutACI7X/GoFLflMgevKDdI5gZL/MnaeY+wCABrMGws2AF/mWQJjFD7nsdwH9AUCEOQdSMmvAOnXLbQpEr70ipf5i2S92XgFAh9l4a2TAlFx6nusiLOPGS4q/5LJf7LwCgA5z7YiZAVNCyUxLIFe8yQ+57HeD7BcABsz8i5kBG1MgOd2GqQd/SdYXy37R+AwAD2YbNDMDNt7B+YwBP+vvmVL8WEllv2h8BoALqXKh4GbAOhbK5y4QPfhLSV/XUtkvFt4DwIYZhJS8XdBlfj0wRu8LZXDXmBvkZb9YeA8AH+b4/ZL3hfS7OJcxYL31lFI9L6WyXxz+ASABb/mwkQFTzhKNYmgem1B17wul6dt8tVhPA4d/AMjA24RgpLCUiEYPcGXRA3P1yPpumdGyAod/AAjB7AJknmjpiCiHPTC694US7c6ZN+cplg4/XgDAOXgn8UZFkxJJLnLqgdFL70m9Lzpq4/UdYewXADmk3o2kSFL3wKS/B0bvfaGo3hh8Y+3ewdgvAJLwugC1wSi1lIx6YG7UM6Uc3xna4r3eOPwDQBLefUjMN7bugUm9CfqB9X3qg1JeyR07/wCQhbcL0EjseHHRE+dZeMdofSb0vpS8Y4KGrwMAkIE3jGUc7BMePVWPTrsJWrc+E6q3Rusfq+EIF34AIA7vOpA5a6zBqCCnvAjQaH0m5K9a8rxy+6fLTxUA0AleQ67R2EtIDVdZNEGr1meK42e8NvGGlwoAIAfrUJ6Z3eXQBK2vvKTk+LpGzmo2QusLAH5gNWUsWL1tZfoB4BWr9Zn38mhw3SUAfuA1wfACHP3oVDdB68k3QqnICI9ZnzJofQHAF6RrLE8YmRnhgKtMfQrOCP8IB6VTmdd4Dv0B4A1WY4aOTSglEN0EneYU3A/OizQT2vqHwQ8A/MFrzGWVQGZpB4DGpR+E8M84G+A8iSXCPwA8wmpNK1ln/GlPwenFB4StzyVrPKbhywAAfMAaA9GtuYQwxzg/TG8NghH+EXJ7meIHBj8A8AzrfEorjPJldI0gvQCQFf7pg1FW8QM7TwHwDSsDZr3T1+kGgHrvFev7Yn24IPsFwD+sDFinaJQ4ST04tQBQ770iGEwfbHKKH8h+AQiAUJBCOOlPNgDUe68IM4K8qpAC2S8AIWBlwLrTg9ADo2f7XxlPQR5W+Md6RRTIfgEIAycDNjp9CdN0OsdLaQ8WK/xbsbrCTyD7BSAQrAxYH+IRxoC1LFJahMoK/7S4OAsWkf0CEApOBrxmjQGnGACywj+jIs5Ye4XsF4BwcDLgFacHJsUAUIV/hIze6H1hjBYi+wUgIKwMWA/pE3pg0gsAdfhHaF+R6X2ZOvzoAABcOBmw0fDh/lXSCwA54d9MZPAXW68ACAunV+OT81VSCwBZ4Z9M7wu2XgEQFs5xFSvqKRMLADnhXynS+4KdzwCEhjOqb/TAuD9Yx0wpBICs8E8HbozeF9x4BEB4GBmbUfXkBE0pBICc8G8l0vuCG48ACA9nWnXLeeenFACywj91lMkpp+O+XwBiwAladBXD/a1fJjQFzAn/9IcAo/dljewXgCgwDq0WnABQyzP2Ghi99tldYaxDAMXG6ScGAJCC07Omk1j3Jmid8sXeA6jWPhMUpotAjGkaDL4BEAuRNy6h9SOVAFCHf1/OjzXCP8bHCFr/AIgF59xeN0HnGwDqWz/cXwiduDIK6Wj9AyAejCbAGWcKLo0A0Aj/nA8xWd+++iIofgAQEcZ9jZwASAeAbwx/cZEJ/whLIE58uvyoAADCMM6uWAdgOgB8puuDydX76Tmwwj96CI3iBwBxYbSu6QIoJwC8ZxiMx516/u7hH+f0U4GtfwDEhVECYQWA+uQrVgCow7/COfwrJSbfUPwAIDaMEggnANSPjRUA3qpn4B7C6e5H+scHih8AxIdeAtFr293bh3Xw+H7FsRidR/XknT8BRMI/FD8AiA+jBLJiTMDqAPCOYzEyL+rfjxP+ofgBQAowSiCcAFC9/+MEgK/qqccJ/1D8ACAFGCUQTgCoO+hirMHSi6/czy7HAq/cFuEfAEkgEcW4B4CzqHtQ9eIr5/S/FNh7hbVXAKQCvYct1wBQj75xwj/63iusvQIgFRgjrGqBibsM5hG3IDAWX0mEf7jxEoB0oK8w4dhAl1FDb0Hg7D2VCP9w5wcA6SDxVuZEUqF7oPXoW5Twb4HwD4CEoL+XOT6ItQWBs/lAIvxD7wsAKcHo5GAIQVdPfrB85orufXb+tiXCP/S+AJAW9B4YjhEiDcExRt8Ewj/0vgCQGvQeGIYS9BDcLc9oTlzTv2mJ8G8Z+0cNAKhA3+PJcIIOhUL2QOveZ+e+H4Hwb4bsF4DkoPfAaCk4z1LoPuAnntMcYPQ+zwTCP7Q+A5Ae9D0wup2DIZRwPdC693lBdhc9/MPeFwBShH4bsJ4Cdnao7gS+4VmtM7r5xTmG07Z2vy+4/g0DANKBHtOs6AFg+B5ovfd56/pQnbqS24XQ+gxAmtDPtBgBoO4FDtQCo5pfnFt+ZgJ7/9D6DECa0JugdQDo3FGyUv98mBaYp2/yU9WNKwIvFAAgLQTCGuc2mtAtMG8CzxThHwA9JEYAqKOqEC0wjOaXKf9lwpWXAKQLPbDRdwG7hlVhW2B+qOfp3PxC/xZPYPINgJQhT8ExgiN9EWSALTD05hdGiFt/kQAA6SEQ2jjHkGXALTB684tz8wv9iLP+GgEAUiRGAKjM8s7V20X0rZeulkb4B0DvIQc3jNHYrfrXfV+EdKP+JWeJMYacT68Qwj8AEocc3dBnY3Ve+Mo23Hn06K/r5peSPuJSe4EAAIkSIwDUZvA7BKxHf51nnbXdyeEfel8ASB5yfKPquM6KmAcaAtbVD9cQVWDxFcI/ANKHHOAwUkR1i7DfPfiq+uFco1ZN2uQlEQj/AMgBcgCoSwSuObQeAvZZAdHVj6XjI43uHuo/jvAPgBwgB4D0FpEwFRBd/XD1s65QU0ffEP4BkAfkAFC3CJPzS48VEF39cP4G1TdGHhBE+AdAHpADQHqYNAtQAVHVD+czvAW79xnhHwC5QA0AdRrrXCVVZ4f+KiC6+kF+cuTmIIR/AOQCOQCkl0n12aGvCojefOVa/Zize58R/gGQD/z3ufNXUKGjry1YevOV6+wHv/cZ4R8A+eB+MXhNFWTJ+NqCRZ79WLN7nxH+AZAT1HP+klwpmHvegkWf/diSH3li6fTaAwDiQj7oV5Mczi0wegZEwHZ11L0fzi0s7OYX7P0DIC+oAaCuY7i2wOgZEB/3gDyTvzPd/OJaNzmBvX8A5AU5ACS3wOhjtjcB31VRt5471zDYzS8I/wDIDWoAqIId5wuGdAXEQwuguvXcVcu6dvHJfUUAAJlAjXboLTC6diJ/E7pefeB674duaaQ2vyD8Gx7/U+wnALhQJ/31GkBXg6pCg/wSBPrqA/pIy5EVml8Gx69+/ir2UwBMqNXOklwv0Imi+BIE1fznmsRqezlfGHdk4vayg/z51a9//hoCzB1qAKj7RRwfOPPWAvh0+sLOc3m6+kH8OED4Nzh+9etffv4CAeYOddWn6hemy0a6BfCN+j3N2Ztfxo6vOsidX/36l19+/gIB5g511bvu93CdNPPVAnilvq6rxejzfEdKhH8DY6e/nf8gwNyhnviTpaHNKbsF8IX/hJwvjDuC8G9g7PW39x8EmDnOHXxH6EmjpxZAtfnP1eg6ICXGwth8MDAO+jv4DwLMHOoaLDXK61pDXnjZAqhn31xLOuRv5AQWXw2Lo/6O/oMA84ba8ksPm1TtWHIG7k59P47NfzNu9WON8G9QnPR38h8EmDfUt706NnONIPWmKMEtgGr2zfUQT89+EKsfGH0bFEp/yn8QYNZQh+A21C8w9zADp2ffXKNRdvUDo29DQutP+w8CzBriyqc5eQZEnbjJzcDpxfeOh3i6dZlY/UDv85Aw9Gf4DwLMGeq5vy4cOD5QL1sWS4BV+us6+/bJfRUw+jYgTP2Z/oMAc4Ya+Zwe79pDo0sOdzL2G11Tvxd29QO9zwPC0p/lPwgwY4g90LoC4hp0qYbhRxn96dUvrlGc3mRILIN/Or7WIF9s/dn+gwDzhdoDTa6A6N4ZoSUwavWLaxSnslfiZwB6n4dDRX8V/0GA+ULsgS6pe6PWwktg9OoXR5OTv4MTuPVtMFT1V/UfBJgt1BYYcvSkskaZBFinv44PJEewR3Dtx2Co6a/mPwgwW4gtMHoLlmPvsHACTE1/6SeYte8D9Ju6/ur+gwBzhdj8Qa6eyibA5PSXXME+8eH4OoNMadBfg/8gwFwhtsDo7jnqAyUSYHL6S376R9D8MhCa9NfkPwgwU4jlTzX84Bx5KXMIJMDU9HfGvfUcm1+GQaP+Gv0HAeYJtf2NfICmHshPgMnpL/n48gg2vwyDZv01+w8CzBPi+IMuoDqeIKoH8hNgcvqrTu+I0S82vwyCFv21+A8CzBLi1WdzagPdQi4Bpqa/5Od+AqO/Q6BNf23+gwCzhHgTpnKA6/oosQSYnP7qzX+05r8F0t8B0Kq/Vv9BgDnCzQFdJSKWAJPTX6VuYvMfRn8HQLv+2v0HAWYIsQVO11AdA0ixFmhq+lsyN/9h9HcAnNHfGf9BgBlCjIKoRQSpFmi1+dlV4OTSzRFUP/rPOf2d8x8EmB/EUzA9Q+HYRCLUAq02P7umv+oAklj6RvWj95zV31n/QYD5QauArKlNxDoBZm2BVpufHTW2oLZuVx8P+sp5/Z33HwSYHcQKyCfx8WuRa5D46S9x9g3Vj75zQX8X/AcB5gZxBoSfAHOuQdL3/jo+kJn+Yvaj71zS3yX/QYC5wTWB4+NFEuDX09dwLN9w09+t42sLMuOi/i76DwLMDGIhlJpJ6gaSF7L+ntWTd2xioWbtJ1D96DeX9XfZfxBgZtDmwMihlGqdeSP77/b0JRzdTa7aHJkj/e01HfTXwX8QYF64zrAdoR6l6Rzyiuq/t9NXcLzChHxoeQSbr3pNF/118R8EmBVcGZAT4Cei/q7UU3cMXbnpL+796DOd9NfJfxBgVtCSQXICrA7R7on+07sP3Jq3uenvCulvj+mmv27+gwBzgtgLR02A1QaWd6L/1O4Dx8ydm/6i+a/HdNRfR/9BgBlBbAahJsBz7hJAtfvAMYxjpr9YfdBjuuqvq/8gwIygLUEgJ8DMJYB6+MMxjFP/Li39RfNff+msv87+gwDzgdgCSE2AVeBIGwGh7j7Qvqalv7j2srd01193/0GA+UBrAVQec8wnF+rfJY2AqOEPqnZp6S+a/3qLg/4c/AcBZgOtBZAaUPF2IOjhD2raTUt/l46vKcgFF/25+A8CzAXiFkCqUVQhgjIC8qKetFvWzk1/MfvWU5z05+Q/CDAXaFsAqRmlriQQ/lHq8Adz9RU2//UUN/25+Q8CzATakRg1pGKNgKjuF8dDSxW/0RbeYPatnzjqz9F/EGAeEHNC6jVIykXuHTDXxKc8Z66+wuxbL3HVn6v/IMA8oNUE1EGeYwFF1RLcbwFR3S+OIav6J5H+AoWz/pz9BwFmAS0B1hNlbgUUbRPnDhhq98sH8XFHkP72EXf9ufsPAswBWlaoO1kcD+PIHTDU7pcZ795fLL7vIwT9EfwHAeYALSwanx7uOEJH7oChdr9siY87snJ8NUEGUPRH8R8EmAG0YzGqVtTjXHfAqN0vjt0vVFEfweqX/kHSH8l/EGD60BJgalqpH3ft9g8Su1/IiXr14aAv0PRH8x8EmD60BJhaVlAdMHdOD6PufqEWaqoPB32BqD+i/yDA5KElwNS2EuIOmFvuP0ebdEb62zeo+qP6DwJMHVoCrNuK3e5R1ztgnG5Bejg9yjHc5O0+QPrbN8j6I/sPAkwdWgJMNMuaNgKnnqzbceOct/sA6W/PoOuP7j8IMHFoCTA1s1QHhy4jcHr4ze0Yb8r7HpH+9guG/hj+gwDThpYAq0SWqiSXEbi704Mcx1V4wx9If/sFR38c/0GAacOUg1tKWlJG4NTwGzXZJg1/LBxfR5A0LP2x/AcBJg0tOaR2FisnvXR+iL743C1U1d0vpOEPzP72CZ7+eP6DAFPGsYR7RI1yOOpTHap1vwZdX3zu5jElMMehkSNYfdUjmPpj+g8CTBlScwi1A0Z5s/sBoNp95VhrUQIjDX9g9VWP4OqP6z8IMGFoS7DUKIebPufuB4DE4z9m9wvS3/7A1h/bfxBgujD9QI3Kuh4AquM/x0o1s/sFFx/1Br7++P6DANOFlADrDhi3UznnHVj6+M/tCaoKDanAjXt/e4OA/gT8BwEmC2k6ltpd4rwDSx3/uZUxmN0vuPe3L0joT8J/EGCq0LajEOMr5wNAdfzndtsSNT498uH4GoJEEdGfiP8gwFQhFUjV+Zpj/cTxAFAf/7kVmlUAR4puZ0h/+4GM/mT8BwEmCmk7MrW+6tgBSD3+I9anj2xdX0KQJEL6E/IfBJgmtBSR2F/n2AFIPP6bEfsTj2D3QS+Q0p+U/yDANCHtQFCOcAsfHQ8A1e4/tzhOD79RvjXsPugFYvoT8x8EmCSkFhGlGMcGO7cDQPUc3RzNG37D7oM+IKc/Of9BgClCahGmpphOB4B6959bjq6O/0i1HQx/9ABB/Qn6DwJMENoIiHKMW2eK0wGg2v3nVsad8YbfMPyRP5L6k/QfBJggbgY7Qswx9Q7ADpeAcI//SJEthj/yR1R/ov6DANOD1CRHrTG4XAKibv51O/4j1maOoPsle2T1J+s/CDA5SB0wa+IBoJocuXwJiL75121Ghbf7auz68oHEENafsP8gwOQgDcmqITG39FlNjjxc/Kvq5l+3IRPe7it0v+SOtP6k/QcBpgapA0bNmDkeAKp/9eJfvac9P+p26gPofskccf2J+w8CTAyeKNwOAHV8dX3prz6e/qZbHqsqM6TjP3S/5I28/uT9BwGmBS9RdDwAVHnz7YW/eEV8erzuP3S/ZI0H/XnwHwSYFiRTEA8AVd58aQcqcfkBr/sPu1+yxof+fPgPAkwK0pwY8QBQNc5c2oF6x/z6pKwe3S8540V/XvwHAaYEaU8A8QBw1nUFgup+dosvecd/2P2SMX7058d/EGBCON4udIB6AKja8y50QKtn5/bkeMd/uPg3Xzzpz5P/IMCEIO0JJZpGhVjnO6B197NTe/aadfyH4bd88aU/X/6DANOBNAJHzDQ7dkC/nP6aW/czMSuvPjeQG970581/EGAykEbgiJWGjh3QavezW/czsSpzBMNvueJPf/78BwEmA+UAUHeaOM3n6g7om3N/TV395pZdK4ORttog/c0Uj/rz6D8IMBVIB4CqWOA2QNytA/r0lxyrK8pglKs/SvgvT3zqz6f/IMBEcLzH8oCqZLilqOrc8NwOaL37mWYwUkaP47888ao/r/6DANOAdA26ahZ2K5+oh72e+Uu3tK89pT3sCI7/ssSv/vz6DwJMA8oOLGKwpbPMM3+JuPxFhaSkhB7pb4541p9n/0GASUDagUU8bOuyAkaVP9zETDySPIDjvxzxrT/f/oMAU4B0AEgstnYpgJz+ilsbM7EkfQTHfxniXX/e/QcBJgCpXkBstlMPay+AEMsfqvuZtPwAx3/54V9//v0HASYApQOQqBvVON1eAFHlD6JZScsPkP5mRwD9BfAfBBgfSsGAmG7OLxdA7mlPi9X9jOHf7AihvxD+gwCjQ2oYUSsQ3MoNlwsgxOkPVvczdv/lRhD9BfEfBBgb0gEgsd3kcgFEPSsnkc1Z3c/Y/ZcZYfQXxn8QYGwoIZOqmLod06kJkLYVWHr5ldPXVQeLpGAWu//yIpD+AvkPAowM5chMd0A7PUxlmm0rsNTyKzeRqfIHpZsRV3/kRSj9hfIfBBgX0sIo2rbRixMgxOVXKq+m7H5eub5gICbB9BfMfxBgVEgLQ5VxiAWQlhVYxLs/iBv5D+Dm35wIp79w/oMAY0JaGK+kQQzUWu4AeT/9dyeREbPx6nMC6RNQfwH9BwHGhJI0qpM8t+xZlVrvGv/z8+k/u9VxWeWPNY7/8iGk/kL6DwKMCKVoQAy5LtwBoqbf3KaSWeWP0vXlAtEIqr+g/oMA40FagUAsgJz+zcfG/0y8+lxNf1AiWSw/yIaw+gvrPwgwGqSmYVoBRPeaXDX957fTfw1Y/kD3cy4E1l9g/0GA0aCsQFAFELcJENVr3DgBR1v+N2eVP9D9nAmh9RfafxBgLCgd0MQCiEpVGyfgTv/RLalmlT/Q/ZwJwfUX3H8QYCQoHdDEAogqVTRNwBGn39QJHmX51cL1xQJRCK+/8P6DAONAWhpKW4F1dgLu6fQf3QI51vIrlD+yIIL+IvgPAowCaWm8WoHldHqoCsDvDf9RlX/dAjl1gkcpf2D3cw7E0F8M/0GAUaAUQFTd1Cnq0t3Gz/X/SCv/rlnLr3D8lwFR9BfFfxBgDCg7oIkrsFTY2FAAVuVfWkhJSeOx+zkD4ugvjv8gwAhQCiCqcODWPn2uAHz6T27lX6KID2D5S/pE0l8k/0GA4aE0zs2YE3D1AjCx/Ks6EVH+6CWx9BfLfxBgcEgFENrUhQq46gVgbvmXcvU5yh+pE01/0fwHAQaHUgChTd2eKQATy7+s6TdMfyROPP3F8x8EGBpK6qhamZ2qJ+v2CeB70rNhTb9h+iNxIuovov8gwMBQSgfECbj2CWC1/NkpGlV1GMr0G6Y/0iam/mL6DwIMC6V1hNh3otLml+p/Of0Ht0SWNf2G8kfSRNVfVP9BgEGhFECIfccqba6ugL5ST8bpadD6sKsPBgkSV39x/QcBBoVVO6DFa9UCMHH5s1pESKnhTBxfJhCQyPqL7D8IMCSU4IlZAH6t/Ifb039wO0+kLaKuPBikR2z9xfYfBBgQyuEZrQA8a7sD+I735Sjl3xL+S5bo+ovuPwgwHJTiKbEA3LYBgXb3bynyHYDUiK+/+P6DAINBip5OD3YrAKsDu0oDjNp+QDtOpLTwLB1fJRCKBPSXgP8gwFBQTs/WtNRTHRtWCsC0p8Ka/sX0W6KkoL8U/AcBhoIyPEubPGvZgKDvPqcpjPINYPotTZLQXxL+gwADwQqfnNzTsgKf2P7CWf68RvkjSdLQXxr+gwDDQDk+o+WeLQ0wL7Rnwln+XLq+SCAEiegvEf9BgEFwi7oO0EbPWhpgaO0vqoOFMsGH6bcUSUV/qfgPAgwBpQCstgdINMDQLv9Y0Z7CAZR/EyQZ/SXjPwgwAJTtecTVU80NMKr9j1ZM2RD8h/JveqSjv3T8BwEGgFUAdjp8a74C5PSHbiJmbT9A+Tc5EtJfQv6DAP1DEYhaH+DkrOYNMKc/JLa/ELYfoPybHCnpLyX/QYDeoUwA0xpgVM76Zvyhan9xK2ScQjhK+o7yb2okpb+k/AcB+oYyP6siOVoDjNkAqPxHbH8hPHvcfZkYaekvLf9BgJ6hGITWAKN6VswrkGiXH6H9pUckpr/E/AcB+oWyAlpFcsQGmCb/OYWSxA6cAyj/JkVq+kvNfxCgXygnaLT0U/nvRv+Zan9xWqbKuvzjw/UVAh5JTn/J+Q8C9AqlAMzcAGM0ANLa/2gHkJUnDxIgPf2l5z8I0CdOc2dHaOXXpgbAd95XInQv4u7fhEhQfwn6DwL0COsIjTa0YTQAnv6IOEqC9pesSVF/KfoPAvQHZQMCbfxC+e9e/dHN6Y9oJ4mU4g2W3ydDkvpL0n8QoDdYDTC0qq1uAKS1/xE3UFeeO4hMmvpL038QoC9YMZSTtho2ANK2/6kvRIld0f6SCInqL1H/QYC+4JyhOWmrYQMgbfsf2v96QKr6S9V/EKAnnDrvDhA3YNX99+P0J7SDRMr2K5R/kyBZ/SXrPwjQD+EaANXiKdUAqNr/aJsU0P6XK+nqL13/QYBeoMxQMBsA6/6jfR1C6FrCfwmQsP4S9h8E6INwDYD1Bmhm+zPa//IkZf2l7D8I0AOUJhJaBFZvgKY9B872P7S/xCdp/SXtPwhQnnANgLUr0K9ozwHtf1mTtv7S9h8EKA4rinKqwKrNo6cGaNX+7LSFdcbxH9pfYpO4/hL3HwQoDsF/tA682gZo2viH+jKU3dXwX2RS11/q/oMApaFUUU+PdRNXdQP0LenLsNqfUf6NS/L6S95/EKAwhC46YgpabYCmbX9G+3O+pK+/9P0HAcpCaQBk+u9KxH8Ecc/hv5hkoL8M/AcBisJJI4u5y6PUxcHHBmja+Afn8nO0/8UkB/3l4D8IUBLWFhXWAAhz/INQuMHllxHJQn9Z+A8CFITVRkIz18vh/7/yvgran7MiD/3l4T8IUA6K/zanBzvloOreouMAiHoGa5evwtn+DP9FIxP9ZeI/CFAMVgM0rXJR9Z/Tv4325xzJRX+5+A8CFCPYAEjlBhDa+Btr+z38F4ls9JeN/yBAKQgN0CIDILTxN9b4x5fjSwNkyEd/+fgPAhSC00ci4j/aF8H4RzZkpL+M/AcBykDx30kkTs0zSl2P+//7dPq/TiZbkR4F/0UkJ/3l5D8IUARWA7TLg9b2AJwa/6AdIjrdmXQA259jkJX+svIfBChBMP+NWvwXbPwN4x8RyEt/efkPAhSAUkigNeHZA8A0/y1JjzqwcH1pAJvM9JeZ/yBAPuEGQD5Oj9oPwL2d/p9TAVr9ywv3Z4325+Dkpr/c/AcBsokwALf3X/jxX/gvNNnpLzv/QYBcKINkykJOq1ti+2/p+MoAJvnpLz//QYBcOCZxOoVTq6v2N2DS1h9wxn8x/hGWDPWXof8gQCaRBoBP/4e4/sD9ScN/YclRfzn6DwLk4ZTDevCf07/M8d+H6ysDGGSpvyz9BwGyIHSSbE+PdWoeFPAf6/ZLtD8HJE/95ek/CJBDsAFgZc0H8voXjP9mQqb6y9R/ECCDOAsQmOtf4L+kyVV/ufoPAqRDUAltC1Wz/4Ktf8Htb8HIVn/Z+g8CJENQCW0LqYD/OOtfMP4binz1l6//IEAqwRYgqADsnew/rD/IgIz1l7H/IEAilBsw+QtgmOuv4L9kyVl/OfsPAqTBaSUR8F+w9VcY/w1C1vrL2n8QIAmK/yanx5KG1+j+46y/gv9CkLf+8vYfBEgh3AIYvv+w/ipxMtdf5v6DAAmwVgk4+e8UNe4WwDD9h/VXSZK7/nL3HwRIgOAS/gJAtf6UtkKL8Jw/HV8W4Ez2+svefxCgOxz/OeWhpv/Cr//D+hff5K+//P0HAToTbAEg/NdreqC/HvgPAnQl2AJAvv+w/jRd+qC/PvgPAnTE6QIiEf89sf3n/pThP7/0Qn+98B8E6Aanl47WvXcXw38T19cFONAP/fXDfxCgE8H8Zy5AfT/+z2Ieyn9Y/+KRnuivJ/6DAF3g+I82vXsXY/09/OePvuivL/6DAB0ItgAV/uspvdFfb/wHAXYnH/+p6z++4L+E6I/++uM/CLAznF2iYf2H9fcp0iP99ch/EGBXhuG/Nfznhz7pr0/+gwA7Esx/i9OjHiL4D+tP/dAr/fXKfxBgN4L5z7wA5PQ/4b+86Zf++uU/CLATMfwX/vpf+M8HPdNfz/wHAXaBIpPTYZrT5SGG/8Jffwn/eaBv+uub/yDADgS7AA7+6xm901/v/AcBXgb+AyT6p7/++Q8CvAinmRj+Gy491F8P/QcBXiLYBZhs/61Oj/qE/6LTR/310X8Q4AXy8R+uP0+HXuqvl/6DAM8D/wFn+qm/fvoPAjwL/Adc6an+euo/CPAcw/DfNvbL3Cf6qr+++g8CPMMw/Ifrz+Xorf566z8IsJ1w/js96hH+y5n+6q+//oMAWwnmP72CD/7LmB7rr8f+gwDbgP+AA33WX5/9BwG2AP+B7vRaf732HwTYDPwHOtNv/fXbfxBgI/Af6ErP9ddz/0GATcB/oCN911/f/QcBNgD/gW70Xn+99x8EWAf+A53ov/767z8IsAb8B7owAP0NwH8QYJVh+G/l+rIAmyHobwj+gwArhJ9/w/6X/BiE/gbhPwjQZhj7D+A/FsPQ3zD8BwFawH/gEgPR30D8BwGa5OO/FelR8B+boehvKP6DAA3y8R/uf4vDYPQ3GP9BgBr4D5xlOPobjv8gQMUE/gNnGJD+BuQ/CPAEQSbw33AYkv6G5D8I8AhBJmsx/33Af2kzKP0Nyn8Q4AGOTGj6ehiNTv+TJlD4LxTD0t+w/AcB7uHIhJa+RvFf4fiygOHpb2D+gwC/B+O/EfznztD0NzT/QYDwH2hlcPobnP8gwID+U+Mbb0T/zWhrZ+A/GsPT3/D8BwF+f4bynxrfvSP6j7h2C/4jMUD9DdB/ECBnmQD811uGqL8h+m/wAiT4b3F6LPzXVwapv0H6b+gC5CyTX1L993j8n0UZyn+T2K9zVgxTf8P038AFyPGf02NN/z2c/ncw/41jv845MVD9DdR/wxZgjv6bwX8+Gar+huq/QQtwFcp/m9OjyP4bkx4F/zkyWP0N1n9DFiDBJTT/KQNdw38pM1z9Ddd/AxYgwSUqksvIfxvHl2WwDFh/A/bfcAXIiaVo9roeje5P/5tmUE7MCs4yZP0N2X+DFWAU/92d/jfNf5wzS3COQetv0P4bqACLdT7+41yACf91Ydj6G7b/hilAVi+dk/++To+6ieG/bewXOgcGrr+B+2+QAqT472SyYu7yKDWBNorhPyyAvszQ9Td0/w1RgMGuv7T893L6307bZ+A/rwxef4P33wAF6HSFh5j/aBfArUiPgv+6Af3Bf8MTYLDrLwX8x1kAPcMCmPNAf/DfAAXIUYnb9vrTox6j+A8LsM4D/cF/ewYmwCjXf2j/TUhfgpO0gyagv1/gvwPDEmCw9c+W/2gLUGn3rsN/l4H+9sB/vwxNgDn5DwsAPQH9HYD/9gxJgJxWYlrzyhv8lxjQ3xH478CABLhwNwl//anhP6dVpsp/To3X8N95oL8T8N+R4Qgw2Po/23/hF2AtY7/SqQL9KeC/E4MRIMckTv5Tj4rkPyxAaAb608B/ioEIkLX+xSl3Vo96EfAfFmBJAf0ZwH+aYQgw3PoXc/0VdQEqLfI8gAG4JqA/E/jPYBACpPjv4/Rghv+wACYJoD8L+M9kCAJkrT9wqt2q9X8S/nO6eP3AHA3QNaA/G/jPYgACjLP+wPCfUw/hQuJpgxPQXwX4z6b/AiSIhDiFZvsPCxDiA/1Vgf8q9F6AnIM0p90FM1H/Of3TRyaxX+vEgP5qwH9V+i7A8OO/r7b/nEzGWoCAARAL6K8O/Fej5wIkNNLRtjDb6w8wABwb6K8B+K9OvwXIGaRwKsKq0oWM/wgDwGiANoD+moD/GuizAIvw478/Kv5zaqLBAJwM0F8j8F8TPRYgJY/8PD2Ysf6APQBHWFuzcHxtegz01wz810h/BRhu/G1zehTLf+qrYACEAfTXAtt/v/kXv4n9PcjzL//V//y/xP6V9QRl/IM5/vZ0+P9vp/+/dfkqnAE43AB3BPprg+u/3/z29/7Xfgnw9/+3P/jDP/rXP2P/yvqCNf5BO7m7Pvx/5gCc09hI5YkPHOivFab/fvPbf/Pzf++PAH//3/67n3/06z/+kz/urf6+Nxz/OT3KHv81/OdURWYNgHy5vjq9BPprh+e/3/z23/zy87sXAvz3/+E//qeff/Cnf/a7X375pcf6o6SRc4nxt9Ho5fT/aV2ElMQdDYDQ33lY/tvp75ef39kL8D//lz//Tz//4i//6neH/9tn/bkdvlUU5CSuddV/tAE4DIAwgf7OwfHfXn87/+UswH/5r/78v/713/zlX+k/6bX+KG10zPGPx6r/vpz+dY7/0AAI/Z2H4b+D/vb+y1SA+1LH3/yt/dT7rT/WGAXt4O7h9CfqOTj966czPFbj9mCB/s5D999Rfwf/5SfAU6mj+uf91h8piqLtoFd3Btf953QDCWcAZPANgNDfBcj+O+nv6L+cBGiWOqr0XH+OuWfFQE6rE6rjH6PR4+lPnExGGz45+m/gDTDQ3yWo/lP6O/kvEwFWSh1V+q4/UhcJLQKzb78ckQdAOA3QA28AhP4uQvSf1p/yX/oCrJc6qvRef6wuYrezQ/v2S8t/tAEQylMfdAMg9HcZmv8M/Wn/JS3AxlJHlf7rjxVEuZ0dVsc/qAMgrAboITfAQH8dIPnP1J/hv1QF2FbqqDIA/VG2n6pDNFrfyvdNzX9OMyjEf/7Ap/ML1Bugvy5Q/Gfpz/RfegI8V+qoMgT9sYqobgFYtf15NHrifSE0ALoA/XWC4D9bf5b/khLghVJHlUHoz22DQcUhTtqa1/2nGqDdRtk4G6BXri9QX4D+uuHuv4r+bP+lIsDLpY4qg9AfL4aiHdup9r/RM+1ZoAHQHeivI87+q+qv4r8EBNip1FFlGPrjLRFw8p+KvLT/uA3QhNHl9TAbYKC/rrj6r6a/qv/iCrBrqaPKQPQXsP2vevvHiNwAjQZAV6C/zjj6r66/mv9iCdCl1FFlKPrjtb84nb9Vt9+PzAZAp7s8WA2AQ2yAgf664+a/Bv3V/RdBgI6ljiqD0R8lheS2/73oP1Mb8MM1AA6wAQb6c8DJf036a/BfWAG6lzqqDEd/pBICrf9O3Rlyrf+M1gBNXL96YHgNMNCfCy7+a9Rfk/+CCZBU6qgyIP1R2l/UJZLE9r9n/We3vC/lVjY5MLgGGOjPCQf/Neuv0X8hBEgtdVQZkP54EZTT1Ma63v5HbgBUQ7yc6HUgQH9udPdfi/6a/edXgJxSR5Uh6Y90gka7gLe2/XnHzekPiUeJhOG9gRWAoT9HOvuvTX8t/vMmQGapo8qg9MeroDrZR2XND+afnv7QLZPlXIE+msR+zUMC/bnS1X+t+mvznw8B8ksdVYalP5JAaNmnyprvzT9lNgCiAeY80J8zHf3Xrr9W/wkLUKTUUWVg+nPruztCqz40tf9RGwCJFZgDS9fXKF+gP3e6+e+M/tr9JydAqVJHlaHpj7JBoGS2/z2Zf3p/+lO3s0ROA8zW+VXKFeiPQCf/ndPfGf9JCFCy1FFlaPoj+YMYfKljt2vzT2kbAFkNMIPZgAD9Uejiv7P6O+c/pgCFSx1VBqc/0vYDWvuLLrtemX9K3ACoZEpogBnKBgToj0QH/53X31n/0QUoX+qoMjz9keoHtOvX5k3tf+Qr0DkbYAZyBQj0R+Oy/y7o77z/SAL0UuqoMkD9kcq/yj1OJYuG7X87rk5/7JaJ0y4grn4DfQb6I3LRf5f0d8F/rgL0VeqoMkT98cq/TrUTlTW/2X9++uPCKZNVNQxKAXgIE8DQH5VL/ruov0v+6y5An6WOKoPUH6X8O6PVXuuX/x6gNcCoaHJC8N8AJoChPzIX/HdZfxf910mAnksdVQapP1L5l6iepu1XI/IGLKKEj99B7wsg0B+d8/7roL/L/rskQP+ljirD1B8ve5Rof+E3wBAKwL2fAIb+GJz1Xxf9dfDfGQEGKXVUGaj+HJ1zgDh729z+Qm6AYW1A+HB8mTID+uNwzn+d9NfFf80CDFXqqDJU/ZG6R2jmaWl/ITfAsDYg9HsFNPTH4oz/uumvk/+qAgxZ6qgyWP1RuodV95xb5tm8/WVE3gBDzMIrD+4j0B+Pdv911F83/xkCDFzqqDJc/VGmx4iVh7b2l9Ho/fRfnHzKKgD3eQIO+mPS6r+u+uvov4MAw5c6qgxXfzx3uD24efvLyGyAcUrGWQXgHk/AQX9c2vzXWX9d/ff9d7/3f4QvdVQZsP5I02/ExLN5+8uOH6f/QqunkArAvZ2Ag/7YtPivu/66+e/v/+HnH/zrXwcvdVQZsv5ItQNu+fe6+l9UAwxRqKwaTs+A/vg0+89Bf5f99w+/9/Mv/vLPfvfLP/5+7G920PojTb/Ryr+ztvKvUQAmJtRLwjfR0wII9CdAo/9c9HfWf3/3T3/413/zt8fjvuj+G7b+KHdfEvPOxsuPDjyrp+P0PFgTwAvXlyoLoD8JmvznpL9W/+1SXqu7L7b/hq0/x467A8Srx1XA9VD/b6f/RDQq5buY9bEAAv2J0OA/N/01+u+U8lpE9t/A9ceLnNw2p7ZtPxiZBWCnjFrVcElRbA8LINCfDHX/Oeqv6j8r5bWI67+h649U/iDevNa2/WBEvgJEDxSzTjF7A/QnRM1/rvoz/VdLeS2i+m/w+uOJQ6r8yy8AszTeF6A/Kar+c9bfyX+NKa9FTP9Bf6TEkXbxxpnyL7kAzLoDuG8FEOhPjIr/3PX3y88zKa9FRP9Bf6TpD+Kp25nyL7kAvBD5PvoB9CeH7T9X/f37//Af/925lNcinv+gP1rcRKy6niv/UgvAM84dmDqO7QPQnyCW/5z09y//y5//10spr0U0/0F/3HMzt7O69unfEXkCWJ8pUibg+rQCC/qTxPRfd/39/v/5B51SXotY/oP+vrnacJPnufKvMQHsNsrBKoBsHV+shIH+RDH8101/TimvRST/QX/f7LTRbe3yufLvaHR7+q9uQaXqKSQl8r05AIT+ZNH+66A/55TXIo7/oL89bg3MB9a0uy/n58q/5BXQ6ho3SgGkN3eAQH/CKP9d0h8p5bWI4j/o7wDl7g9V/iCWal8b/7O+A92pqDxnFUB60gEN/Ulz8t85/dFTXosY/oP+jlAuDiKWP9qXPx94PP13t45sVgFk6fhypQn0J87Rf63646W8FhH8B/0doVx9rssfxErFXfN/VwVgYlWFUgDpRQc09CfPwX/N+uOnvBbh/Qf9naCsTaGWP9ru/j2hJuDcKhmsAkgfVsBAfx7Y+6+uP6GU1yK4/6A/BcUZxPKHHrZ4bv4L6g5gt0oGrwCSfwc09OeDn3X9Caa8FqH9B/1pKDkjsfyhHvbe8hf0BJxTJYM3AbJxfcVSA/rzwk9bf8Ipr0Vg/0F/GtK1Qdzyx0Pb31BPy+1ZqS1+lE02uXdAQ39++Kn05yPltQjrP+jPgHJtJLXgoKomd21/I0YBZJ73ASD054mfe/35SnktgvoP+jOh7H6mxlvqpO2l7W+oCTi3U0liPFr9bnIE+vPFz9/+Xx5TXouQ/oP+LCgR04y2/EqXP27a/srL6W+4DaWoHhZSNJvzCgTozw+/+7P/+//xmvJaBPQf9GdDOTEj1ltV+eO79a/cnP6Gm8iI9egjGR8AQn8e+JM//tM/+PkP/+1Xv/1NqH8xnP+gPxtSxZTYb3e5/EFdAUjtRzyQ7wEg9CfNb/7213/0h//99/av7j8HE2Aw/0F/FSjLD0Yfp0cTyx8/2v8OsQCielgow8zZHgBCf6L81V/+zV//v//9f+jXN5gAQ/kP+qviNr92hHb1eYfyB7kAolJYUgd0pgeA0J8Yv/urv/yLn7/3D9VXOJQAA/kP+qtBOf4raVefdyh/GAUQN5HxVsDkeQAI/cnwJ3/2p3/w8+//vvE1DiTAMP6D/mqQdKHO8dyS5w7lD7MA4vbMlFspQs/yABD6E+A3f/zrP/r5D7/X/iqHEWAQ/0F/dUjHf6rb2C157lL+IE+AEJ/TkQwPAKE/Lr/527/56z/8p/9x4XUOIsAQ/oP+GuDZwi3W6lL+IBdAeB3Q+R0AQn8smo/7GgkhwAD+g/6aYGWLjvemq/LH07m/pVZguYmMuJHhSHYHgNAfmd+dOe5rJIAA/fsP+muCVy1wK1HMLi2/OqBWYLntJVwTa9IHcjsAhP5o/Mkf//r/O3vc14h/AXr3H/TXCClZJPbaqRm197N/Ta/AcosuiT2JR/LaAQj9EfjN3/7NH/3hP/0d5fX2LkDf/oP+muG5wu2WcjU08nD+76k7QNxGOdSX5zk9B6A/V/7qL//xrzse9zXiW4Ce/Qf9NUPKFWfEWdtLd3+ceDv9PbfaDG8Fwsr1pYsI9OfC7/7sT//i59//M/M19yxAv/6D/logXf2xIj76/NXnGlUAcevNmbEOADO6BAT668xhkYFDqaMdvwL06j/orw3K1R86VXR7tC4xXJ3/i+oSdMdAjpiWH/lwfOmiAf11w1xkIIFXAfr0H/TXCskURM+oqvHrpb95+ouOgRzRy0dyuQUY+utAbZGBBD4F6NF/0F8rjhXWA9TjP9VifH/pb76e/qZbdYaalx9Y5JEAQ38X+J1DZ7MjHgXoz3/QXzukXSlUzXRZ/nKAuAKGKuYjWfgP+jvHn7h2NjviT4De/Af9nYG0K4+YZs66LH85QOyA1oYldfXkMAIH/bVycZGBBN4E6Mt/0N85KMNv1OO/jt3PI6sD2i2QU2YmdQBmMAIH/TXTbZGBBL4E6Ml/0N85SMNv1Cyza/fzyOyAdlOsysxJHYDpd8BAfw34O+5rxJMA/fgP+jsLKUyiHv+p/pK7y39XdUC7Jei8DsDkR+CgvwrOiwwk8CNAL/6D/s5D6n4hHv+tu3Y/77g9/V3HAg3vADDxDhjoz4S2yEACLwL04T/o7zy0Mqna/edmz067n0/oHdBuDTq8EeC0O2CgPwVjkYEEPgTowX/Q3wVI3S9z4vFft93PJ95Pf9ttBYIeAaacbCa9BBr6O8BdZCCBBwHK+w/6uwRp9bOqkjoe/6nlBz+6/G3iAeCadQlIyh0w0J/UIgMJ5AUo7j/o7xIFzxGOQ2bKTE9d/rZageAYo6oiC8nt6e6AGbz+BBcZSCAuQGn/QX8XIU2JaY+5ZaZl1+UHB/QKBOIBICm3X6d6ADhs/UkvMpBAWoDC/oP+LkPaElASr/5Qx38Xlx8cUM/SLUilPr0jie6AGbD+vCwykEBYgLL+g/464BbAHaEGWG7Hf8YlcI6JLDE8PZLmCMhA9edxkYEEsgIU9R/01wFaiZR6wOZ2/Ec/AKQeTx5I8hakIerP9yIDCUQFKOk/6K8LpIvPqQVWx+M/+gEgtTx9JMERkMHpL8giAwkkBSjoP+ivE6ThDz1h6xY9uh7/GQeAbonsnDcCl94IyLD0F26RgQSCApTzH/TXCVqBgLphxfX4zzgAdNzRpSI4UgdMcgnwgPSX9nFfI3ICFPMf9NcNUvqrJyQcB2xdj//oB4AboW8wDQaivyiLDCQQE6CU/6C/jvDCI8fhN+fjP/oB4IL4uCNpXQM8BP3FW2QggZQAhfwH/XWEtvtAHeM5VhfUsVrn4z/yCPCa1wGT1A6E3usv8iIDCYQEKOM/6K8rpPkI3f3ieCinHtf5+I88AqyPGkkdMCklwP3WXwqLDCSQEaCI/6C/zpDSX2pstXY//jN2ADrGmswOmHQS4P7qL51FBhKICFDCf9BfZ2jpL7X7RZ3JdT/+M3cAuj1XfUZJ2u+QTALcU/0ltshAAgkBCvgP+usOLf1VwxWOtVXK8Z9xAOjYqcjrgEklAe6j/lJcZCCBgAD5/oP+HGCqgWqkDld/aO5Pj3LsNeTtgEkkAe6d/pJdZCABX4Bs/0F/DtDS3wWx+2XmcvWH5kU92zBPs/rwmPRKf4kvMpCALUCu/6A/F2iRETWw0mtFnR6mbwF2PMhTAiON+KWQAPdHfzksMpCAK0Cm/6A/J2jpr0pjHTtS1LHhm9vjHolPl3pMeSR+AtwT/WWzyEACpgB5/oP+nKAlhuTCqgqobt0e9+P0OEeP6TI1aQQkegLcB/3ltchAAp4AWf6D/tygpb+qsc7xPE4Pv924PfBJ/YNuHmOOgMROgLPXX/+P+xphCZDjP+jPEVr6q4Y4HAcr1NDco+u/qJ6wo8eoz/RI3AQ4a/1lu8hAAo4AGf6D/hyhpb8zalSlbHTv+k+qHViOHtORKmnHddQEOF/95b3IQAKGAOn+g/5c4aa/blIhDb8doI7AkVV9JOIW6Ez114NFBhLQBUj2H/TnjOPuviPUqqrufnEYfjugR+AclzlTK9VH4m2BzlF/fVlkIAFZgFT/QX/O0Kqia2pXnTpNcxp+O0DtgFECo+1AiLYFOjf99WuRgQRUARL9B/25Q+uKIzeVqGqq0/DbgXvic2buQIiVAGelvx4uMpCAKECa/6A/ArSpCGr6O6d2v4wYHTDcBDjOPcD56K+viwwkoAmQ5D/ojwA3/XU8PVSp6DvhX71Sz9pR2swEeBYjAc5Ef71eZCABSYAU/0F/FGg9cTr9dWyeoXe/jBgdMAtmAjx2fVX5ZKC/ASwykIAiQIL/oD8StJYQlf46Ns/M6N0vI7MDxnHmRJ860hLgleOLyid1/Q1lkYEEBAG6+w/6I/FFagnW6a9jJZbR/TLi7IBRVWdaArwOnQAnrb9BLTKQwF2Azv6D/mgw4yHX9FfFjQ+kf1d3wDg+b24CHHgGLl39DW+RgQTOAnT1H/RHw7WTuKox19kRFUc57n458YP6DzMT4LAzcInqD8d9ZFwF6Og/6I8IbfaNnP5qjTzT/KevQXcMPJkJcNAWwAT1N+hFBhI4CtDNf9AfFdrsGzn9VRZy3v1yQt2CRDUvMQEOOAOXmv6wyEACNwE6+Q/6o0Jr/qOnvyoLdbj43IY6AsJNgMO1ACalPywyEMNJgC7+g/7IOF6ldoSc/s5pNx+Z6FuQHNXNTYBDtQCmoz8sMpDFRYAO/oP+6NCa/8jpL2v44wB5BISbAAdqAUxDf1hk4AMHAXb3H/RHh9b8R09/VRGBNPxx4O30NQInwOsga/AT0B8WGXijuwA7+w/6Y0A0AT/9JQ1/HOAnwLQt0EFaAGPrD4sM/NJZgF39B/0xoC2+p6e/eo0KafjjgBoBcU3e58wt0AG2AEbVHxYZBKCrADv6D/rjQNv8p1cYuKa/6oGOF//avJ6+iuvmhgn1gdWn74to+sMig2B0FGA3/0F/LGhx0Iy6+Vk/8IXjP70DwTGPXVIfeMR3BSSO/rDIICzdBNjJf9AfC2L1Q1985HgAJ5L+iiTAtJWvnisgEfSHRQYR6CTALv6D/njQqh86jXRNn1X+SNx9cIKfABMTf68VkND6wyKDWHQRYAf/QX88iNUPchQ14+4+OKF2ILjmseTItfatyxNUfzjui0oHAV72H/THhBgEkU/RdPpL3H1wQl2D6ZoAz6iNOyf8VUCC6Q+LDBLgsgAv+g/640Krfug2YtfsU8mDcPGlzSP7KdDW3virgITRHxYZpMJFAV7yH/THhVj9WFC76MTSX0YCrDsXaTNwviogAfSHRQZJcUmAF/wH/bEhVj/0GoFY6S8jAdaTK9zvXhTf+sMig/S4IMDz/oP+2BCrH3SDyKW/jASYbO8jXrZg+dQfFhmkynkBnvUf9MeHtvmKnkEKpr+MBJicvZ/wsAXLm/6wyCBpzgrwnP+gPz4F8f1PriAIpr+MBFhXb6j+Fw8A/egPiwzS55wAz/gP+hOAWACld5BIpr+MBFh379BaAMXvAfGgPywyyIQzAmz3H/QnAe3eD7o+RNNfRgI857YATmV/DsL6wyKDrGgXYKv/oD8JqNGPSh9dm6dF019OAsxtAZS9CV1Sf1hkkB+tAmzzH/QnArH9g14+kE1/jQTY9RxPz8DRWgBFW2DE9IdFBpnSJsAW/0F/IhBvPdeL713TTuH0dzS6U8/EMZLV8RtxC6DgELCM/rDIIGdaBNjsP+hPBmLxc0Zu/tOnZiLpr7kEy3WXFXcNvlwLjID+cNyXPc0CbPQf9CcDtflF546u8aOqmjJXX2nUEizXk8gFcwug8QV4MPWHRQY9oVGATf6D/oQgHv1ri7l+gbnM5mcTvQXatZRD/i5qX4AFR39YZNAnmgTY4D/oTwpi80tZUL+AapvhbX420QmwayOLysWpp6AiW2DI+sMig97RIMC6/6A/KYizr8bJmWvIpdpmWBcf2TycvqZrGEc/xTwisQWGpj8sMugndQHW/Af9iUF925Mrpwv59Ne8B9g1jCNXsU/we6Dd9YdFBn2mJsCq/6A/Mai9z/TOORU4voulv6PRlfqGXH3OroCwt8A46g+LDHpPVYAV/0F/chD73uh1A50v3kuI78Tb6at+BftOTjB7oF30h0UGw6AiQNt/0J8c1FN/evVD1wueBLSneFLfkWs7j66AEGdAeAFgZ/1hkcGAsAVo+Q/6E4TY+2ycmrnmz+qRjxLa07yfvq5rRDvjzoCweqA76Q+LDAaHJUDTf9CfIPyQh26bHxLW09yr78m1jkF3+ZGSHgBe1h8WGQwTU4CG/6A/SahHXvScUa9+uZGwnkYvgXGtYyzIufwJcgB4QX9YZDBgDAFq/0F/klBH3xg1A/VIodUvGrUExvkqY/WcnGsnR6h7oM/pD4sMho4WoPIf9CcKNfxbkSOmufTqF41eAuN6mZOOZtmfB0606g/HfcAU4Ml/0J8s7HzP+cRM94oIrX7R6Bk41xZA3ctNLQeRAsBG/WGRAVCcBHj0H/QnC3X0jTEz5mP27YSagXNOY/UsH+0eUFIAWNcfFhkAm6MAD/6D/oShhn9aF67dgysfs28n9AycaxpbcoeAjbJOVyr6wyID0MBBgHv/QX/CUMM/PcHhfH6oEud3Ad9VuSK3AOqF/NQhYOctCKb+sMgAtLEX4M5/0J801PBPxzqucZZOnEVn307oFkDXY0l6QeeE4xaEk/6wyACcZyfAf/x96E8carev0S7iGixpSQg3/x3QLYDOFqOHtEfcAsC9/rDIAHTgn3/7m3/8fehPHOpR14J+aa6/5r8DqgXQ2WL6KmNqC4xLAPirX2ORAejKP//2X/xb6E8acvhHb35Z+Gv+O6DX4LuOpczYLTAOAeCvfmKRAejOP/+E/sShhn9z+roANWgrt/jeRm8BdH5u9Jr2ic4B4E8c9wEn/lvsJ9A/yOEfXRSeqx8jcwug87fHb4GRWIQPAAgB9V2uJeZ8xqYDJNHNfyZqC6B7BUS3wFA/GfiL8AEAIRB4kzsXClT1Q3jznwm9ArKil3WOIAAEIA/4SZ5z84uuftzxPdeGXoLgvNmQ/p2dQAAIQA6Qwz+9vd45wdTVD/HVBxq9BMG5AjKlf2tHEAACkAPU8E/nsM4G1QeHHlYfaOgVkDX9ZPMEAkAA0occ/i3oVdIA1Y8RrwKiK9vUHmgEgACkDzn8073PzouiQlQ/RqwKCKO0fQIBIACpQw7/SvqURJDqx8iqgDhHcfoiJGoPNAJAAFKHHP4xBKGvCPJY/RhZFRBnRzP0fgIBIABpQ+7vYCSIgaofI9YWLCO9JweAtJtAAACBoLZ3cAoEar/K9zXXb5e4Vv+Uc5y7Yg/Bka+CAwCEIEb4p8/FPFc/RmYFxP07pbf31L8EACA9yOHfkv4ldNu0p81XJmoLlvuIHgJAAHoNOfxjjL7p5QLvnjZfmeh7QNz7WNT3iAAQgP5B7u3lzIeV/jdfmegKiHMZQ3+T5ACwRAAIQKKQW3vXjNBI33ru5d6PKowhYE6Qe2Ls9AMBAIRCIvxzjox04eSBabaOqJvQ3VXN34KAABCARPkgO4UR/mmneLj1vAk9BOy8y09vQaAHgBunHwkAIAzkvl5OZTRo88sBRguMlhc5AJwhAAQgQchzXZzOON384nn0V6NbYLZ0edEDwKXTDwUAEAL3rS3KYYzRWN0QEqD55QCnBUYgAMQaBADSg9zTYYR/zhl04OaXA7oFxnkPvkQAOEUGDEBifJF7ejnhn24HCdL8coCxBUYiAEQTNACpIfB2dg//gje/HNAtMM45v0QAiCk4ANJiIvBu5kRTgZpfDjC2wIgEgGiCBiAl6K3PnPBP99MFa345oFpg3AvWEgEgmqABSAl66zMn/NO9zwE2v5i8qH/Y/T5ziQAQTdAApAO99ZkV/qlOkBCbXyxUC4x7ECcRAM7QAwNAMjivAlBwwj/d+/yDJTMC6iIkQhAnEQDiKhAAUoG+z44T/hl9IJ6vPapzpf5p9x5oiQAQV4EAkAr0OIYV/sXofT6he6DdKz8SAeACJRAAkoDe+8IK/6L0Pp/QPdBxAkD0wACQBIzelykj/Cvj9D6feFPfvvMQnBEAupePT8wRAAKQAPS9L8baZ0745/3WyyauGd+/DgAZJ6fYAwNAfOh7X4wqJschgXufT+ghOHd36wCQvjUCe2AAiA89hWOFf5FG3zR6D3SkABBjwADEhnGEn3X4Zw7BuUfAEgEgSiAARIZR/DCCIE74F3j0TaOH4NwdxvreT6AEAkBc6MUPVgyk/RF89E3D2ILAin0VKIEAEBNG8YN1Bqb1Eezajzr6IhB3gbPOPutfBAAQHnrxY/SpvgjHHhHDP+MiEJbB6btzUAIBICKM4kcpE/4F33xgorcgsAJA+hEqSiAARIMw+tDwznUfgjXyvuCbD0xYAeBWfQC4T9ApcBswALFgHN0vODOwOvyLsPnAhBMAGrPP9DUIuAwOgEjQr3wzd1flG/6ZASDB4iuJNQi4DA6AKHDCFtZ7P5nwz1iDRVllpY8AGGUk3AUCQAwYdUvW4X864Z+5BovgcVYJSIG7QAAID6NvzYjgCGf/CYV/YgEg/QIBNAECEAFGzqav7yFUkI33e4S9p1VYAaDIFBxWQQMQHM6ZvU7ZCBVkHf5F2XtahRUAsl4IBZoAAQgLp/Wv5IQ9RvgXZe9pFV4AKNIEjdswAQgL48DKiFcIXXOJhX/MANA4CGXE01tkwAAEhJP9rjhlz9TCP2YAuOb0QWqQAQMQjmJLf6/y3vLJhX9mAEgIZ41GSEYPDDYBAhAOzmm9NhglXkou/DMDQEob34d6NOdIAWNwAISCE6rwjvwTDP+YASCrGKT5cPoBAgCocLJfo+WD0PqcYvjHDQD1HkTGQA0yYAACwcl+eeFOkuEfMwA0mqA5nyvIgAEIASf7NfI0gkXTGv3Q8AJA1oGoBhkwAP5hRSms3hfjvp8EJn9NfqgnRihiGAVxxkwhMmAAAsDJfo0AjtD7Yiw7jr74xcZYBE3I6o0PBfp1UsiAAfAPK/vVARwl1dOlk8TCP3MRNOXjYcx6tAIZMAB+YWW/OkWj9L6kG/5xA0De66JfIMwBA+AVVoDCi3MSDv+4AaD+1lglEMwBA+AT1ttzxep9KRMO/6wAkBDCGeeinBII5oAB8Ahn65X5Jie0yRnv7TuOInzxop4e5TrLrUwJBBkwAP7gjKgyix86/Hu/4jwLbzyenh/pDE/XLjhTINgFDYA3ODvqzMkPSg1FCyLJ8M8MAHl6Z1WYjGk6AIAkrAF9ZoSzSj38MwJA0io/oRLIGvcBA+CDgnU0v+XtOdFv61vOs/DJtXqKFIMZp6OsUwbcBwyAD1gHUzNe8UPPNjxynoVfHljfIvMDQrGEAAEQ54tTmDTOpZjB0QvnWfhFB4Ck+8z1AQHrnBVjIACIwzuWZx7v69LxK+dZ+EbvwaI0QRsLDDh3gYzmaIIBQBjWoZRxKk8xgzH5ltDa0zrGHixKCqstz2oCNGpFAAAJWEVJ7jtbJ89JrT2to/dgUQ5LjSyfNWaIJhgAROEdyRuZHaWGnPbkmwlvCs7oXuadNqAJBgBBCtaBFPdkX0++Jbj4wEavQSAFzPo7Za0Zwy5UAARhtb4YzSuk+WGj9Tnx8I/dBD0TagLELlQAxOAFI8w3tZHMJTr5ZqKn4Eg9MFveR4UGm2AAkEHsrUjyqL4eKNnJNxPdBE2K4PRRAa/itEYTDAAS8ObezJCGcqg/y6L1WWM0QVNqRsbJHS8DxhwcABLwDv+4TR16L0DSrc+ae94LZ7QKsWruOAIEQADe3JvRi0Zq/SszaX3WGE3QlB4Y47iTNwaHI0AA2LBu5LFa2kg9NPpN/MZ6HgFh9sCUvGZJDY4AAWDCPPwz3oOkbHCVTeuzRjdB0wQmlgHjCBAAHrzDPzP7pbyXDX1m0PtygtkDY3zTzAwY98EBwIHX+WdeR0EKhTLrfTmhe2BIJR/uq6bBIDAAdJidf9xIxtj7kkXvy4lr5gvIjJo1GAQGgAxz7Jf9PtbFj0x6X07oHhiS9+UyYOwCBIAKbwsTO49bZNf7cuJZl0BIHyHGd05ZpW+AXYAA0GDGHtwoxkjekt/7UkX3wJBKIGbkzDuCGG0gQAAI8JYQ87Nfo/iRTe+LQu+BIQXRxmcHbw4Y14EAQIHZ+Gw2X5CyX6P4kVHvywluCWQhNQeMNmgA3CmYB08z7hm+Ln4kfOVlO2/Mb18uA8YyVABcYTY+G/qiZb/5Fj8OcEsgZgbMa8JEDQQAR7inTtzs1yh+ZDP4a8MtgRgfAMw6PGogADjBrX0YORct/cu6+HGAWQIx54BZtyGhBgKAE9zah/GGo2W/86yLHweumS+nEQJzP45QAwGgM9zax2jJ3HqVe/HjgC6B0E4TjP0t3NNY1EAA6Ar33cZ+424zL34cMEogtA8U42OEuQgBNRAAOsKc+zCzLVriZnyBTIsfB4wSCG2PAfcYwWAJAQLQAebCe6t1jXZwr+/8yLb4ceBVfSMT0uONtJVbkMcuLAA6wG23NVMtWuHTSJ9vmc8lMkYJhHYOuhUbAxmtUQQG4BLcnVfmyiVa465R98xs7VUdvQiL1gRotpFzm2BmKAIDcB526dc8s6JFkrr1L+fixwHjLhBaLDzjnqUaoAgMwHm4pV/znJ2WsRnv0h/cJxMf4y4QWvxmjIF8cI8AUQQG4Bzc0q95dkf8Wjp+zOrOjzb0XSDECoYuBnF3oeJOdADOwS79mq0vtI4N48T/ift2TwGjCZAWD5tjINzSlClTAIAFu8nMPK6n1VGM864H7pNJA6MJkKYvswmGuQkGRWAA2mBP/Zr5FXFniRZo5q1/Gt0ESMyAt3JzcBAgAM2wZ6zMwz9ipLLqTeuf5ka/xMQDvLHgzwg3wgHQBPt03Tz8o6V6xlfIvvVP80O/LLQDBv4La4AuGADqsLsrzAkrYphifIUb7rNJhyu9CZBYE+cH1gboggGgCl9/W3an2qIHW/+a0GNw1A+GpeARIAQIQAV254sVo9C+mJHmZbz1rwljDI5YYjemathHgNiHD4AFX3/mGRWxjmxkv9kPvtkYY3DEDHgmeQSINkAADATeUrpGSd1UsujV4JvNk351iOHbgh1eG6ALBgAFv/HP7PwjHv6Z2W8fBt9s9C58aga8FBwEhgABOMFeeWUd/lHXlPQ3+x2JZMDmJW7sViXciATAAf7KK+t0ihhL9jn7HYlkwAIvsgH6oAGQ0Z9AaNLv7HckkgEv+EG2AQQIgEQ/mdlPQT2a6nf2O7IzYOpdHkvB60AsnQIwUPhn6WY/LbU02ffsd2RnwNS7PIwiO3G/hAn6oMHQEdDfnH8uNet99juyM2Biv5HZZMlvg4YAwcDhT5Na+zmpR4lGXNPT7HckkwGbdXZ+DcQcWQRgcPA7aS13UY8StwPIfkd2BkxNX7eSbdCYhANDRuIdtOSvJpkPIvsd2Rkw9TZLo1DEvqwFAgQDRkJ/xhESeXu+0T3T4+x3ZG/Con5YmJMbAjUQCBAMFIGhX6v2QZ0imQ4k+x3Zm7CohwVz2RoIBAgGicT5uVH7IPd0GPuIX/ud/Y7sXdDUz4uFbA1khFFgMDxE3jpm7YN4GGUqtEc7n9swMmDyecFUtgaCXQhgcIjobyPwRjRW0fVq53Mbxm1I5PqFUQMRmAOBAMHQENGfWfugniUayVyPbjw6h74PmLzGxRSWwPgiBAiGhYj+jIM78lmWMfjRm/t+L/HK/zGYNRCB9RUQIBgS5OYzE0Nd5NqHeYDYm/t+L/FsjIFQx2/MGojEDxMCBINBPGQgDxEbJ/lvAs8pE16MV476NaYCZw8mECAYCCL6s87gqbUPI4N+73/ri8YYAyGv3t4IvP4mECAYBDL6WwrEH2brS88HP2yMRQjkJhjTVxKDcBAgGAIy+ltJnD8ZIWTvBz9sjDEQcgOLef4qsMUMAgQDQEZ/ZumX/AVXg2t90egxEHrtyPghCFyIBAGC/iOkPzP0EPgi7wMY/KhgNMGQ42czCJeYBIYAQb+R0Z/I0ZP5RQbT+qIxmmDodxkthSeBIUDQZ2SiBHNgnl56XOovMqDWF43RBEMvXxgnqORCigUECHqLTIxgTv3SW88WA2190dzrHwz5CHAt8lHU9hUB6BFC+ptKlH7N4uWgWl805iYYkddRpAsGAgT9REh/K4nSr5lCD2LrSxPGJhj6EeBcugsGAgR9REh/xgVkjNUjxsH9g8jTypJb/VLSYzfzDktyHm2xxkZo0DeE9CcTbpiHf0PZ+tKEnoNjqGsrEpBbQICgXwjpz7h/m3Hebh5aPcm8Y/NE5AjQqkeRx4lbvyQA2fMl0hxhHQ3Rj6zMrzKwubcqIkeA5hIxoc85CBD0CJnWCKtqwXijGZtLBjf3VsU4AqRvsrc+liSWYdlVfgCyRkp/G5Glmysc/hkYR4D023xljiVsVhAg6AUTD/qjH7TPcfhnYh4B0g/v5hAgAI3IdIVZdUZGtdLc+Tfww78D5hEgPXc125Jk+qBHo8VXx98wAJJFSn9mOMD4msbE6uAP/w6YR4DU60DEfjwWcwgQ5E3xmZb+poMf+61zL/LKbiFAAGykGmKlDpjMNG2gY791rvQuQM4e0430OtRdpyZm4UC+pKY/s/F5sGO/dW6MXYCMyyxl6lM2GAYG2SLVDGvpj3FIb76ZBrnzrw1jFyBnjZ/ZBigmQHRCgzwR099apu/ZbHx+xOGfiXEdCKMGYv2chCbhMAoC8kRo5q3ytmLkZ8YJ/QAv/DiPcQTIKF5IfVDZbCFAkB1SbbD2m4qRVpm1jxep92ZfMG4E5hQvrIMKMQGiExrkhlQLhJj+zNrHvdQ7sz+YbdCMGNuPAEv0wYCcKMZS+jMP7Tj7NU2NovG5AaEaiFCpqvpVJ91+7wBIALHqn336zYkpDY2i8bkRow2aUQOxklWxMxD0wYB8kKv9ienPPENH7aORK5kaiC8BogwM8kDu3EdMf8bC+yFedt4N40p01k0efgRo3tkCQLII/sqb+uPkZHPUPrpg1kA4IfzWjwBRBgbp88HwVIWl0PvIqn3g8K8dswbCCeI3fgSIMjBIHMHCr1weZdxPgdrHWe6FXnFPApyjCgJSRrDwK6e/DWofnTFqIPT7QPwJcD1GDgySRbDyYemPcTeZ/YUw93EBYx0+r4XdkwAxDQySRWzit6o/jlbNsTfUPi5yIzMI50+AqIKANPnw9FvO0p9Z+n2Qe379xayBLDlfyDyrkxQglkKDBBHbdD+S1B9Kv87ceXjpRQWIpdAgOQRnPgT1ZwYh2HnVEaMIzDp49SZAzIKAxPgSrHwI6m+D+z7cMQfhWNryJkCsBARJIXXF+R45/U0x9kbBXAbIKgL7EyBaoUEySDY9S+pvhdIvjZv0BYhDQJAIvDJhFTn9zY2vhNKvE0YRmNcF40+AOAQESfAltuVyJKs/I0VC6deRW+OnwPp48ydAdAKCBJDs+rMLFjz9rVH65XDv4+cgLEB0AoLIFBvJoz9P+sPUL4EH4yfBivA9ChDjwCAqnKtyGpDTn3VtCKZ+CYh1wVQEKDgjWanwAxAW2U9zUf0ZX+pO9EkOBnMdtKAAJXdk7DZ7IwcGcZBtexHV3xadL3zEumC8CnD2gRAQREB04m00Wn/K6c+sDOKySzJmF0y6AsTFICACwgc5ku8Qs/EPnS8MTAFybkSqdOsJnxojBwahkc59RfVnvB3en2XfagPD7IJhrva2BCi4JnyEHBiERjj3FdXfzNQfOl94mAJkjvn4FCDqwCAgwrmv3cjKnCexVPok/DYbHGYXDPfczqsAsRABBEJ00+mopj9eT42lPzT+sfEmQF5BpQ56oUEQpE+vRfVn3nWJxj8JzDZA3iBIJUuVFiCWAoIAyM77VsfYufrboPFPmhuxPujKz1r8NwnXAwPPFEvpT21v+nsTfqKD5UbwByT6066xRisg8InsrquRdEo0ReOfD8w2QFkBCtfR0AoIPCLd9Fe9zpqrvxX05we5QZDKaa/0KAjKIMAbX9KFD3vmTVR/79CfJHeCPyZbgOK/U6MtQkAgTyF+XF3peuZuErT0h75nWYw+aL4AJ9aPXfy5YhoEiCM98VEt17HfB3PozyeSApT93GsA0yBAlon4SXUlD+LeoWR9NYx9yGPsg+ZdiVQVoHwj4O6jFQYEYsh3vVQqdexzcEt/GPvwgDUIwhbgxmsjIJZiAUE8BH92HwT0lwGiArQL/+KNgLuB4EmXX20ALuDjgKby68/1q6W/W/lnC0biApyKfgA2gGZoIIB8y3M1/WF/+q/Nj3pMvXnDGgVmC9DOAOTLawgBARsfJ3/V4++Z5JeD/jxyIypA+wRYvg8GISBg4uPkr1L4ZVf/oL9wWAJkty4L/yI0UaIQDKh4Cf6kP/ahv5BYAmQf2s2sRGDioQqCXkBAxUvwZ29pE9Yfhn69IyvA9YffdQgj9AICGsXUR/BnF34l3kDGl4P+AvAi+vOT/n1oBBPBwBEP076j2nYOfmkZ+guPsACnsvlAIzMshQEufPloR6hMvgt0vUJ/MRAWoN0H46UKgr2AwIFizG1J6fJLyP9Nh/7iICxAqwzsqQqCVhjQFR8dz6Na5YN/iRz0FwthAdpl4C8/h4BohQFd8NP0Up35YO97gf5iIixA+1jYyyzICHUQcBlPdY/qZ7zEe8b8glj4FxhhAVbKwNIXTJ+YfSIEBGeQX3F/xD7jEWj0gv7iIi1AqwriZSPWnnICA4IWvGx68fPbDf3FRlqApf0J6ekMejSaIgkGTRQfXtrvR7XsRuAWOegvPpYABY7s7NvLvR0CIgkGTXhLfSv30QhUPqC/JLAEKNC4bN8G6O0QEJVgUMNf6ltJbCRSJegvDaQFWFla76kTcIRKMLApxh5/1azfaYlN59BfKogLcGUfAnrqBEQ7NDCZePw9sw9bJKabrCE66C8q4gK0JyQ9jQMf/iXMBIPv/ayvt9S3eqYtkWRbnTTQX2TEBVhphWYvBz/DAr0woNh4/A2z0xn+wuCq/h6hv9hYAhQpWdiZqb9GGBwDgmLsreel2vYis93S0h+G3hLAFKDMVeaVT01vjTA4Bhw4xcTnh2ul7UUkk4H+0kNegJVDQIF+0XZmGxhwoHha8nfEXnYlc5JtjZFAf4kgL8BK1czPXvwTJQohQ8TTfvsTdmIhs9gc+kuTa+NOEKGuPfvWIq85MAohA8Rr2aOa+wq9J6C/VDEvRRJp8ax1zXvNgUejFW5KHxK+FjyfWPj45Z1Cf8niQYCVj1CvdeDRaI21CIPBb9G3VlQTSl6sc+p76C8tbAHK9NNXfo0E5sbPAQMOg8LPtb4au+dZ6vDa1p/f7wC4YwlQ6B7LSiOMz17oEZphBkHhcdbN4y+tPRUA/SXI1av5Y5dZJlT9KPX9u4tmmH7j336VzgWhpMXaeAD9pYktQJnJXfvSGJ87sY7AgP3Fv/1GpV1HEzq0tu8OufP9TQAaPgRYSSd87sQ6AgP2FO/ZQ61rS+jAxr475MX7dwGIXN2bP32hlpVKDuy5FXAEA/aTid9251G9Y0GqYAf95YMpQJlRkHo7ge8yCAzYO4rJyrv9qpmKVMOW1fX8/uT92wAcLAFKJauLym+W/0QGBuwRAc796oUPqQ5rW3/Yd5U6t+ZvgUwndD2z8F4GgQF7QxD7VcaV5E5pNtBfZljbEIQaAasny76nQQ7M0BGdPWHsV+0clWqxttsfXp8DfCuAy5PVCS11q2D1A3YZIATETEjmFOMQn5O1Ep1UemK3/WHkNxPsURCpKzwqByxin7EX/tUtdsPkiu853xPV3ETqI9/W6hv0lws3j+Zvodhh3TZGCLjbDQMDZkix8d4qumdeOZv+kPpn7b4XDH1khNUJLdUHUyuDfIv9ql0AG1SvPvgAABOeSURBVFKz42vpv0tqTyX4k2tPtdtpMPSRFVcP1i+jmKe8/bZdYI5icEYUAZqdj78XH74OZexr09H1nBtWI6BcubYyYCmXbVxitkQpJA+KjxAl3z3Vj2O5vqwNup4z54f1myH2K1nZiBAuBEQpJAuKzzBFj4bgT+5Tfv2Btr/ssRsBpcrAtWkQ7/sszX8aB4FpE+zYryH4k7uiwS78ou0vU6xrkQR/PaqdMKEKwaP9VAjS4FQJMuV7ohb8SXW9VFtd0faXLVYjoGS1thICBuoFPIA0OE0CJr5NwZ9c3GlN/KLvJWfsPhjBa3xnlUS02IT75B+NFp8wYFoUIRPfXRXOW/BnVz7Q95I3V2/Wb6lgYa7SDB1mIlgxW35BgclQjINVfEcN076STQj2RR/oe8keqw9GsApSDwE93+laZYVaSBp8BRr0OFE9fJHsQLArHyj89gC7DCx5j3ktBBTMQrqAIDA+xUfAmsf+h/7pL/irVj6gvz5glYFFL/CohYDBuqFPIAiMSRE69Gv4zJVsP7VnPh5Q+O0H1joE2YO66q9jyFaYA7MpysFxCHzqN2poehE9c6l09qPw2xvsMrDoxEa1F/B7ErQOsmeB4eDgFJNp2OPehrqH7IGLvezv+zb0dwc8YldBRNfX146jA9dBdqxXH1BgQL42IXv9jqy8/qLZ264w8dszrGtBZBdXVSeCA44EG8yWyIPDUIwDlzwOP9/qSa9sw5Xd9PyIykffsLbiC//yVJbCBB0JNp/GBvVg3xSTbfjwfncRQvUzVjSHqXyEo/LRQ278HQI2/XrGeJeMRitMhnikmCxD13sPLCofsMIDlzP76A+Vj15i70QVvsNyXktPQq3ArLCGAv1QRDn0a/zdKmR/typXe2Hmo69YKwFFOwEbWmFiVIIPzLaohghTfG1i/TRrVd9iLPyLa319zHz0mBf7EFC2f6vWChOjEnwEChQkovzqVV/pKaPKby2W/fUauxVachx41LCWI0I7tAYKFCGq/EZl9WcovWiocnXwPSof/aZyCPghHKFNPX9auzHb4iyQRVz5jWa1zirpxoJKdImm5/5jHwJKr62qtWlF6oU5sSuHwIEkisky7o+u/mEq3Vhq+/X9Oua3CwJhHwKKNyvXehUiHgMenxH6Ap0pPqZxWl0Uq9rvkXRPVeU6axz9DYTKIaDkSqxRUzNg1GPAA/MlDgM7UxSfUZqcTeoHf+J5RGVwE11/g8HeCi25F//ArHbsJp65EJ7UFplwB4pJ1CO/cL9AlbYadP0NiTv7d178l6teCZ4EX5bUwAJDwmcpxtvIWe+oaaDcw/UyldwXA78Dw16KKp4DN1SCi4+op+knEAa2sAv8Yh9TjBrLHh5+cyq5LwZ+B8ezNQ4snwM3fYoLN+6TKXEaaFN8xT/xO1Ire0i36Y/quS8ueRsidiNMsRT/B2pzm9/FJpF32WiNVPhIUYxjl3o1i9oPRXjWd1TPfdH2MlAqjTDSvdDNv85BLwo+z2y1GbgDd+5L4lTiQFn/wPSwR6jS84y2l8FyU8mBPRT+aoc50RbDNDNcBybmvkb7eTgyrh7L/Ij9bYN4XN3bv28egrP6MWBiBhyNZouhnQcWX4m5r2HW7fvbR8vA3D5exJ77gWPnwLKL8Y/M6ndUejjSZrIup+NhTIkUk00KTS42s0CfkvauK+S+oJIDFz72FdSa+RNpB6wyX216HQjuUt4kelwqNNjPyznxeozcF1So5sDyrYD7roY8DLgPBD97eCJYFB/LVXJh354m+3m5PqHS9IfcF+yxr0byUgZpKoSkasC+SbAoPjbbxE77NE32G/t4ttU90sh9wZFKL7SfHpV1VgY8SvAj71mRohinGvUdaLKfn3sTKoUP9DwDA7sXWvpqkCONv+wpG3BHuVrmWBgpio/P6SKVbvMWAv5CVHYSoecZWNjzwL6uMM/SgPtQcJuNBQ/mSznoO9Lwy+BrXXhl4uP7DfO+wMZejP9d+OiEaW6GycKAo70FV9NxwhlxUYw3WZhv1GY/T42h1asJseYe1LkN8lHc0Ob/XaTWEX2WWblNS4NF8TX+nK7KnF7DgPardr28YtcVaKLaCuhrb32DAZObCbnMLhrcjCcRPVgUxXg8nZbJ1nbbaPoF8LDn4ECl6+X7B3Jf0MxVpQzi7fK2RgMmsxvGjXVZTqefOxEGMuFee8vptswk063SaD9fP/zqpdRo+gNnqLQC+mmGHoV+EwSiLBfT6WbsQ4XFXnrj6XRblnm/SKtJyB98NfjDolNwlsrVIJ6aoUdtBvTS+RqBdVmW2+l0Oh4fdOgoxP0jio/xeLyZTqerMr8Ut+Vl2Qa1Xy34Q+EDXOK2EgJ+ejuYazTgRybFYEdm5Z7V9AKLw9+L/XT9vAb1DnivQX81+EPhA3Tg2e6E8RgCNhtwss2tFAI60FTy/f5aerNfNfjDxAfoyF3FSB4vMG8yoM93BYhD88/ZY9G/GvzhhjfQmUonjL9C8O6d8dnwzig2/UwBh8m6qejh1X614A9dL8CFgCFgc2bU14PA4dF87Dfx2fBZC/4w7gvcuH4MFwK2GPBrijQ4e8rGH63XkccZgj/AptoM7Wsi+MC6KUhAGpw5zYlvMfYa2lenfbHrBZCohoCelsIcaewN26XBqAZnymzZ+Jnmt8ezduk0dr0AIrUQcOI3HFs13r6BanCWLJoqvt5nfCp7/jDvBjjUQkDP15eXzW8avwkTEGc2bbw+wPeJbln9V+8R/AEO1RDQ+9WVjaWQ7wJBYEYsPht/hhPPRxm1q6ZR9gVsqr2AXlthRvtSyARBYL7M2n58/saIDqyqp40o+wIJ7qq/yl7rIKPWg0AEgenTHPp9Fxvfy7pqi8UR/AEhaiGg5zpIW9sYysFpM1t+NSe+3pfbrqt1j+87BH9AjMpSGE8XVFvMps0XDn2hJzBJ1s1Be5Bzi0XlckusegGyVJfC+J0HObIaN7+jJhgMSY1Fc7z+/eU98W2Y93jHqhcgzVM1BPwIEIfNN1+Nb6tijDw4HebLScsHVYhVZrXJoYfn2C8I6CG1Vpgg++qbR6j2/7rvgiLowmz70fYDCnFQUWv5Q8cz8ES1G/r7y3sleNReC/kuQiRX4Bzr1WfLzybMGUUt9UXHM/DIXTUJ9l8JHrXOBu///SUUGI22Q7/v4jNIbF6v+r6i6QX4pFYHCVAJ3lO2nAR+Fx9bVEMisNh8tX0mBbrAYFWt+qLuAbzzVE2Cvd1cXaGtwQIKDE+r/MKtLKstevl+Q90D+OeqOg/ifSZYMV9+QYHROSO/YO3ptVnf70fUPUAYbmpJcIhemAMtA1ZQYCDOyG8Sbjyxvi0X8x4gHC/v1V/+QMeA+2LIR7sCUQ7xSbv8grYjLWrVMLT8gaDUmgGDHQOO9oOmk9b34WSJ+Tgf7FpdWl/0kO3o9R2RSH1BcGqV4CAjcYryTCjytVlgOkSU2Xbc9mrv6r0Bzx3q+yHfkfqCGNQrwZOgIxntR4G7fByHgVKU7dF26AbMescfqr4gFld3tWNAv5fbVGkdP/g+HAYiE+ayXrXH2eEHcLa1sge2/IGIPN/X3hMhhoIN2rsCD29Q7EmgM5+ef20DD2DXJ8Hfb2O/RGDgXL/W3hjLwMqZbc+8TREG0jgb+EWQX9PVWFhwD+JT64X5/gpYCj5wQYFfOA10olyefznDr96pT3ug5wWkQf0YMGwp+MDZs8DdQT1S4U7Mt+MiMfk1XQqIgz+QDA3HgJMIt7Wda1E7psJojDnHfPv5df5DJEZ/eX3WDQd/IC2uH+rvlSj7SRebs+9gOLCN+eriKzeNMVyzrs+6YdgNJEetGzB0M4xifqZfDQ5s4lLcF6+fssl+9zj4AwlSL4TEMuDZeYVTeLpZoSayq3VML7ov2jxNk/0ecPAH0qShEBLNgKP1YjO55MCvz+mQe2Nmi+XZWkfErHfPqm4/lD1Awlzd199A0Qx4qGV+X3h/Fx/LIQaC5fbz0sdD5PbxhouvHl9iv2wAnOU5LQN2CQN3b/TxdDgngvPV8uNS2Lf7qcUL/Jrth/X2IAPqpeC4BtydBl464to/ycnntOy5BOeryxnvoc8l7sdBo/1Q9AVZkJ4Bd7OsXd7438VkPF30Mh0uu6kvhcJQ05XPuNcS5EO9GSa+AXelzk4G+C6K8XIb/dmKMSunnx0S3uOgYOwF2utpk/3Q8gKy4iVJA3Z34HexCwVXCTxjButy2y3oS8R9zR0vsB/IkEYDRpiKq9PZgcqC2R0Lzsrpctzh0PP4XU42CbivxX5o+AN50mTAGHPBTZSdaiI6OBpvposUFHGJdbnqmu0ev7Vk+n9mS9gP9Ip6Q/TOgKlEU7NOjSBmMLjXYJrR4LzcTj87x7Unq6dT8W7Y8QL7gcxpGAmJsR/wDOX0ciNwzYMf4+V0USYRNu28txlPnMR3bPtOKJ4tYT/QS5oNGO6q7C7MFtPuh2V2PDieThdleJGsy3I7XY7HhaP3vo+nfWnVdcox7Af6ytXtY8ObMPDVOZchSvAgwmKyM+G0LH2KZVaWq+l0Mx5/Eax3eJ4p9ng3tfvBfqBHvDQZMIV2mCqzhXs6XFXhzoU7Ge5syMuR52VZLqbTXaC3cx7veY1Ti/r2NLb7wX6gZzQaMJVicIV1ud24VRLO+3AvxPF4vJwqVqWF/g/Tz8Pf3T9O5CnsW3gSrV3PNg0l3+972A/0jkYDJlUKsZmVUzELRqL4SnqQpWy8sQXdzqCfPD00vUeTOwi02FuQeCwYUXwZtG03X9wM+4H+ct1owDSmQs6x7yrOIRjciW85jVCQdn1BG4/93u9gP9Brbu6b3rWTbdKhyomdBt177QKJ72M8zUB8e+ZN3X7YcAWGwPN94/s37TTYZrb34IdQgYKnveLQgpjF58eBVeNSbux2BgOhsSU6hzS4RrkfviB1ITOtt+83XGUS7xnMpo2HqQ+wHxgOV03F4F01OKmxEAfWp9bkiS8XFvtQb9dDU+ZnvRPNFd/vNzS8gIHRWAz+Lj6j3Jouyrrcy3BnQ3of3/5hX6euwUXGztOvy7axs/wdJV8wRK6bDwIn2QaBrczbupxPGJ3R28Pf7d1r0Ljh4Pv7EUUPMFSeGw8CexEEApOW0O/7Fcd+YMg0HwT2MggcLi2hH+bcABhdvzUfgCEI7AWzltAPvc4A7Hn+0ZwGJ7YlELizaC74IvEFQHP18tocBH4ksywfODNffiHxBaALzdXg3WAI8uAcWW8/muWHii8ADTTuid4XQ5b5N78NjFVL3vv99hT7qQGQKk/NtZDv4mOLo8BsmG9a8t73H6h5AHCG57vHFgV+4igwB2bLtvsDMOMLwGXagkC0xCTPrO3Q7/sRoR8A3Xi+bQsCv1ANSZb1qk1+OPUDwInr+/dWBaZ7o8VwWbdWPL4fbxH6AeBIW08gCsLJcUZ+7+j1A4DG84/HdgUiCkyDM/L7fnhBrx8AdJ7a8mCcBabAOfk9YsIXAC5XLw/fUGCKtFd7d3nvTeynB0A/eL59bVUg+gLjMJ+2yw/1XgBEuWk9CtzdnITpkLAsWpuccegHgBduWo8CUQ8JyHr1+dUuv1c0uwDgiadzCvzaIBP2zfzMkR/kB4Bvzinwu/iYojPQF+vF5kzW+/34AxUPAPxzXoEIA30w347P3eIJ+QEQjrMK3IWBOA2UY706G/h9v95BfgCE5YICvz63SIUFKJcfZ69vx5kfAHE41xSzLwpvVmiMYVBOx8VZ+T1AfgBE5Obu9RsO9MB8e67NZTfh8YY+PwCi8/zy9g0HSnLRfd+P95jwACARrp7uH+FAEcrpJfeh3gFAclzKhHcORE3kHOvFpfM+ZL0AJMvVy9ma8N6BxXhaoj+wxny1+bigvu/v1x9YZgpAylwOA3f9gRsEgop1OR1fSnn3gR9qvQCkz+XTwEMguFwM/kSw3J5vbT7ygBM/ADLi+eXtUiq875IeT4cqwXKX8XZw3+uPJ5z4AZAdN7dvl9/eQ5Rgue2mvu/HeyS9AOTL9d1Dh/f5IR3e9n9oeFZOP7upD+4DoBd0deB3UUw+p4ueVkbK7bJDmQPuA6B/3Nx2OQ88WvCjXxYsV9PxpFvQtzvvg/sA6CE3Lx3qwpYFV3lnxLNDzNfVfLs6L2odAPSXq6euyfDRgl/jzXSRmwZnu5Cv4znfKeV9u0VzMwAD4Ob2/nKTdFWDu2gw+SpxWU5dxbcL+348IeUFYEBcXd+9dc+GlQcn4810VaZ2OLjee29cuIpvf9qH1mYABsnz091D17KI7cFiPF5Ot2XcUeLZTnufDrWNivqQ8QIwdEiRoDbh13i8mU4XwVQ4L8vtznofhGjvxMMPqA8AoLi+vXcpjDQHhZPx+HM6na7KUjJFLstdnDddjse7Yi5ZenseH+6ekPACAOo8P929uVVGztmwKD7G4310uGNRnmisoszVf94e/v7uoXvfMY1nBX3obgEAnOVGzoLtaqzh7997f7i/u0aBFwDQmZvru3tScSQhHh/uEPMBAKhcv9y95afBx4cfd9c45wMASHD9dPfjwWdSLMTD290txAcA8MDV3oPMQrEHHvfeQ08LACAAN9cvd3cPD+S+QRkeHu7v7q5xwgcAiMP19dPdLiYMlhw/PDzc7YI9VHQBAAlxfX19e7eLCx8kSyaPD/s47+7pGid7AIBMuLrecbfnx4PivU1yB94OD3jZPzj2twD6zP8PJ5on761/Ri4AAAAASUVORK5CYII="
              />
            </defs>
          </svg>
        );
      case 'arrow-left':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
        );
      case 'alert-circle':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );

      case 'help-circle':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case 'lock':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        );
      case 'eye-off':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
            />
          </svg>
        );
      case 'trash':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        );
      case 'x':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        );
      case 'users':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        );
      case 'campaign':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"
            />
          </svg>
        );
      case 'chart':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
        );
      case 'trending-up':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
            />
          </svg>
        );
      case 'trending-down':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6"
            />
          </svg>
        );
      case 'award':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15a7 7 0 100-14 7 7 0 000 14z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8.21 13.89L7 23l5-3 5 3-1.21-9.12"
            />
          </svg>
        );
      case 'file-media':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      case 'file-text':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        );
      case 'box':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
        );
      case 'refresh-cw':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        );
      case 'package':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
        );
      case 'cloud':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
        );
      case 'tag':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
            />
          </svg>
        );
      case 'facebook':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
          </svg>
        );
      case 'zap':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        );
      case 'bar-chart':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
        );
      case 'database':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <ellipse
              cx="12"
              cy="5"
              rx="9"
              ry="3"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
            <path
              d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
            <path
              d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        );
      case 'google':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
            <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
            <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
            <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
          </svg>
        );
      case 'sms':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        );
      case 'message-square':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z"
            />
          </svg>
        );
      case 'bank':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 21h18M5 21V7l8-4 8 4v14M9 9h.01M15 9h.01M9 13h.01M15 13h.01M9 17h.01M15 17h.01M13 21v-8a1 1 0 00-1-1h-2a1 1 0 00-1 1v8"
            />
          </svg>
        );
      case 'robot':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 2v2m0 0a4 4 0 014 4v4a4 4 0 01-4 4 4 4 0 01-4-4V8a4 4 0 014-4zM8 12H6a2 2 0 00-2 2v2a2 2 0 002 2h2m8 0h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2M10 10h.01M14 10h.01"
            />
          </svg>
        );
      case 'webhook':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        );
      // Social media icons
      case 'instagram':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12 2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153.509.5.902 1.105 1.153 1.772.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 01-1.153 1.772c-.5.508-1.105.902-1.772 1.153-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 01-1.772-1.153 4.904 4.904 0 01-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 011.153-1.772A4.897 4.897 0 015.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2zm0 1.802c-2.67 0-2.986.01-4.04.059-.976.045-1.505.207-1.858.344-.466.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.055-.059 1.37-.059 4.04 0 2.67.01 2.986.059 4.04.045.976.207 1.505.344 1.858.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.047 1.37.059 4.04.059 2.67 0 2.987-.01 4.04-.059.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.047-1.055.059-1.37.059-4.04 0-2.67-.01-2.986-.059-4.04-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.055-.047-1.37-.059-4.04-.059zm0 3.063a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 8.468a3.333 3.333 0 100-6.666 3.333 3.333 0 000 6.666zm6.538-8.469a1.2 1.2 0 11-2.4 0 1.2 1.2 0 012.4 0z" />
          </svg>
        );
      case 'linkedin':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M19 3a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h14m-.5 15.5v-5.3a3.26 3.26 0 00-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 011.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 001.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 00-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z" />
          </svg>
        );
      case 'threads':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12.186 24h-.007c-3.581-.024-5.329-1.576-5.729-5.069l1.307-.118c.314 2.727 1.365 3.854 4.426 3.887 3.098.034 4.294-1.189 4.64-4.75.13-1.345.13-2.78.13-4.35h1.3c0 1.595 0 3.063-.138 4.446-.396 4.113-2.052 5.954-5.929 5.954zm-5.327-14.021c-.194-2.357.568-4.193 2.147-5.169 1.316-.812 3.02-.932 5.116-.363l-.431 1.234c-1.69-.467-3.092-.384-4.073.245-1.085.67-1.584 1.982-1.426 3.765l-1.333.288zm9.847 1.828c-.703-1.061-1.716-1.837-2.87-2.193-1.339-.413-2.686-.404-4.11.03l-.406-1.24c1.734-.533 3.42-.536 5.068-.017 1.513.477 2.832 1.485 3.725 2.85l-1.407.57z" />
          </svg>
        );
      case 'telegram':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M9.78 18.65l.28-4.23 7.68-6.92c.34-.31-.07-.46-.52-.19L7.74 13.3 3.64 12c-.88-.25-.89-.86.2-1.3l15.97-6.16c.73-.33 1.43.18 1.15 1.3l-2.72 12.81c-.19.91-.74 1.13-1.5.71L12.6 16.3l-1.99 1.93c-.23.23-.42.42-.83.42z" />
          </svg>
        );
      case 'function':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M12.49 10.2722H14.22C14.3659 10.2722 14.5051 10.3333 14.6076 10.4419C14.7101 10.5505 14.7675 10.6977 14.7675 10.8519C14.7675 11.0061 14.7101 11.1533 14.6076 11.2619C14.5051 11.3705 14.3659 11.4316 14.22 11.4316H12.49C12.3441 11.4316 12.2049 11.3705 12.1024 11.2619C11.9999 11.1533 11.9425 11.0061 11.9425 10.8519C11.9425 10.6977 11.9999 10.5505 12.1024 10.4419C12.2049 10.3333 12.3441 10.2722 12.49 10.2722ZM19.01 7.84778C20.6503 9.58921 21.5788 11.9148 21.6 14.3316C21.6 18.7316 17.29 22.2316 12 22.2316C6.71 22.2316 2.4 18.7316 2.4 14.3316C2.4 9.93158 6.71 6.43158 12 6.43158C12.3 6.43158 12.59 6.44158 12.88 6.46158C13.04 6.47158 13.19 6.48158 13.34 6.50158C13.4043 6.50368 13.4679 6.51678 13.5279 6.54025C13.5879 6.56372 13.6433 6.59716 13.6914 6.63919C13.7395 6.68122 13.7795 6.73112 13.8095 6.78644C13.8396 6.84177 13.8592 6.90161 13.8675 6.96358C13.8758 7.02555 13.8726 7.08856 13.8582 7.14923C13.8438 7.2099 13.8184 7.26712 13.7833 7.31769C13.7483 7.36826 13.7042 7.41125 13.6536 7.44446C13.603 7.47767 13.5468 7.50053 13.4883 7.51158C13.3483 7.53158 13.2083 7.55158 13.0683 7.56158C12.7083 7.59158 12.3483 7.60158 12.0083 7.60158C7.41825 7.60158 3.70825 10.6316 3.70825 14.3316C3.70825 18.0316 7.41825 21.0616 12.0083 21.0616C16.5983 21.0616 20.3083 18.0316 20.3083 14.3316C20.2865 12.3223 19.5242 10.3932 18.1683 8.91158C18.1214 8.85788 18.0865 8.79592 18.0656 8.72933C18.0447 8.66274 18.0383 8.59284 18.0468 8.52379C18.0553 8.45474 18.0784 8.38823 18.1148 8.32833C18.1512 8.26844 18.2 8.21643 18.2583 8.17558C18.3166 8.13474 18.3831 8.10587 18.4535 8.09084C18.5239 8.07581 18.5967 8.07494 18.6675 8.08828C18.7383 8.10162 18.8056 8.12891 18.8651 8.16839C18.9246 8.20786 18.9749 8.25868 19.01 8.31778V7.84778ZM15.5 13.4316C15.6459 13.4316 15.7851 13.4927 15.8876 13.6013C15.9901 13.7099 16.0475 13.8571 16.0475 14.0113C16.0475 14.1655 15.9901 14.3127 15.8876 14.4213C15.7851 14.5299 15.6459 14.591 15.5 14.591H12.49C12.3441 14.591 12.2049 14.5299 12.1024 14.4213C11.9999 14.3127 11.9425 14.1655 11.9425 14.0113C11.9425 13.8571 11.9999 13.7099 12.1024 13.6013C12.2049 13.4927 12.3441 13.4316 12.49 13.4316H15.5ZM9.5 10.2722C9.64591 10.2722 9.78508 10.3333 9.88759 10.4419C9.99011 10.5505 10.0475 10.6977 10.0475 10.8519V14.0113C10.0475 14.1655 9.99011 14.3127 9.88759 14.4213C9.78508 14.5299 9.64591 14.591 9.5 14.591C9.35409 14.591 9.21492 14.5299 9.11241 14.4213C9.00989 14.3127 8.9525 14.1655 8.9525 14.0113V10.8519C8.9525 10.6977 9.00989 10.5505 9.11241 10.4419C9.21492 10.3333 9.35409 10.2722 9.5 10.2722ZM7.5 10.2722C7.64591 10.2722 7.78508 10.3333 7.88759 10.4419C7.99011 10.5505 8.0475 10.6977 8.0475 10.8519V14.0113C8.0475 14.1655 7.99011 14.3127 7.88759 14.4213C7.78508 14.5299 7.64591 14.591 7.5 14.591C7.35409 14.591 7.21492 14.5299 7.11241 17.9213C7.00989 17.8127 6.9525 17.6655 6.9525 17.5113C6.9525 17.3571 7.00989 17.2099 7.11241 17.1013C7.21492 16.9927 7.35409 16.9316 7.5 16.9316H9.5Z" />
          </svg>
        );
      case 'folder':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
            />
          </svg>
        );
      case 'tiktok':
        return <FileVideo2 className={iconClasses} style={colorStyle} />;
      case 'dollar-sign':
        return (
          <svg
            className={iconClasses}
            style={colorStyle}
            fill={fill ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        );
      case 'project':
        return <FolderOpen className={iconClasses} style={colorStyle} />;
      case 'statistics':
        return <BarChart3 className={iconClasses} style={colorStyle} />;
      case 'reports':
        return <FileText className={iconClasses} style={colorStyle} />;
      case 'folder-plus':
        return <FolderOpen className={iconClasses} style={colorStyle} />;
      case 'bar-chart-2':
        return <BarChart3 className={iconClasses} style={colorStyle} />;
      default:
        return null;
    }
  };

  return renderIcon();
};

export default Icon;
