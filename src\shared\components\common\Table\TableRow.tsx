import React from 'react';
import { TableRowProps } from './types';

/**
 * Component hàng của bảng
 */
const TableRow: React.FC<TableRowProps> = ({
  children,
  className = '',
  style,
  onClick,
  onDoubleClick,
  onContextMenu,
  onMouseEnter,
  onMouseLeave,
  ...rest
}) => {
  // K<PERSON>t hợp tất cả các lớp
  const rowClasses = [
    'border-b border-gray-200 dark:border-gray-700 transition-colors',
    className,
  ].join(' ');

  return (
    <tr
      className={rowClasses}
      style={style}
      onClick={onClick}
      onDoubleClick={onDoubleClick}
      onContextMenu={onContextMenu}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      {...rest}
    >
      {children}
    </tr>
  );
};

export default TableRow;
