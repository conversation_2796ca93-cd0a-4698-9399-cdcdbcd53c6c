import React from 'react';

import CreateUserForEmployeeForm from './CreateUserForEmployeeForm';

interface CreateUserForEmployeeSlideFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  employeeId?: number;
}

/**
 * Component form tạo tài khoản người dùng cho nhân viên trong SlideInForm
 */
const CreateUserForEmployeeSlideForm: React.FC<CreateUserForEmployeeSlideFormProps> = ({
  onSuccess,
  onCancel,
  employeeId,
}) => {
  return (
    <div className="p-6 bg-background text-foreground rounded-md shadow-md">
      <CreateUserForEmployeeForm
        onSuccess={onSuccess}
        onCancel={onCancel}
        employeeId={employeeId}
      />
    </div>
  );
};

export default CreateUserForEmployeeSlideForm;
