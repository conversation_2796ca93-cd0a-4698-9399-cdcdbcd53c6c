console.log('Starting simple test...');

// Test database configuration
const dbConfig = {
  host: 'localhost',
  port: 5432,
  username: 'postgres',
  password: 'postgres',
  database: 'redai_dev',
  ssl: false,
};

console.log('Database configuration:', dbConfig);

// Test TypeORM options
const typeOrmOptions = {
  type: 'postgres',
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  autoLoadEntities: true,
  synchronize: false,
  ssl: {
    rejectUnauthorized: !dbConfig.ssl,
  },
};

console.log('TypeORM options:', typeOrmOptions);

// Test connection string
const connectionString = `postgres://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
console.log('Connection string:', connectionString);

console.log('Simple test completed successfully!');
