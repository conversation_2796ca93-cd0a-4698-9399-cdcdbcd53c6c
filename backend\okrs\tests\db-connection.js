const { Client } = require('pg');

async function testConnection() {
  const client = new Client({
    host: 'pub-ai-erp-yzkr13ma-1099002.dbaas.bfcplatform.vn',
    port: 5432,
    user: 'root',
    password: 'dFmTCcoF8xZn0H21uBrZZrWZ0xI5OULiUA8i',
    database: 'postgres',
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully!');
    
    const result = await client.query('SELECT 1 as value');
    console.log('Query result:', result.rows);
    
    await client.end();
    console.log('Connection closed.');
  } catch (error) {
    console.error('Error connecting to database:', error.message);
  }
}

testConnection();
