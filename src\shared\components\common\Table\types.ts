import { ReactNode, CSSProperties } from 'react';

/**
 * <PERSON><PERSON>i sắp xếp
 */
export type SortOrder = 'asc' | 'desc' | null;

/**
 * Cấu trúc cột
 */
export interface TableColumn<T = unknown> {
  /**
   * Kh<PERSON>a duy nhất cho cột
   */
  key: string;

  /**
   * Tiêu đề cột
   */
  title: string | ReactNode;

  /**
   * Đường dẫn đến dữ liệu trong object (hỗ trợ dot notation)
   */
  dataIndex?: string;

  /**
   * Hàm render tùy chỉnh
   */
  render?: (value: unknown, record: T, index: number) => ReactNode;

  /**
   * C<PERSON> thể sắp xếp không
   */
  sortable?: boolean;

  /**
   * Chiều rộng cột
   */
  width?: number | string;

  /**
   * Căn chỉnh nội dung
   */
  align?: 'left' | 'center' | 'right';

  /**
   * Cố định cột
   */
  fixed?: 'left' | 'right';

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Props cho TableRow
 */
export interface TableRowProps {
  /**
   * Nội dung của hàng
   */
  children?: ReactNode;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties;

  /**
   * Sự kiện onClick
   */
  onClick?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onDoubleClick
   */
  onDoubleClick?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onContextMenu
   */
  onContextMenu?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onMouseEnter
   */
  onMouseEnter?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onMouseLeave
   */
  onMouseLeave?: (event: React.MouseEvent) => void;

  /**
   * Các thuộc tính khác
   */
  [key: string]: unknown;
}

/**
 * Props cho TableCell
 */
export interface TableCellProps {
  /**
   * Nội dung của ô
   */
  children?: ReactNode;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties;

  /**
   * Căn chỉnh nội dung
   */
  align?: 'left' | 'center' | 'right';

  /**
   * Colspan
   */
  colSpan?: number;

  /**
   * Rowspan
   */
  rowSpan?: number;

  /**
   * Các thuộc tính khác
   */
  [key: string]: unknown;
}

/**
 * Props cho TablePagination
 */
export interface TablePaginationProps {
  /**
   * Tổng số mục
   */
  total: number;

  /**
   * Trang hiện tại
   */
  current: number;

  /**
   * Số mục trên mỗi trang
   */
  pageSize: number;

  /**
   * Callback khi trang thay đổi
   */
  onChange: (page: number, pageSize: number) => void;

  /**
   * Các tùy chọn số mục trên mỗi trang
   */
  pageSizeOptions?: number[];

  /**
   * Hiển thị bộ chọn số mục trên mỗi trang
   */
  showSizeChanger?: boolean;

  /**
   * Hiển thị nút trang đầu và trang cuối
   */
  showFirstLastButtons?: boolean;

  /**
   * Hiển thị thông tin trang
   */
  showPageInfo?: boolean;
}

/**
 * Props cho Table
 */
export interface TableProps<T = unknown> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * Cấu trúc cột
   */
  columns: TableColumn<T>[];

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Loại hiển thị loading
   * @default 'overlay'
   */
  loadingType?: 'overlay' | 'inline' | 'skeleton';

  /**
   * Text hiển thị khi loading
   */
  loadingText?: string;

  /**
   * Cho phép sắp xếp
   */
  sortable?: boolean;

  /**
   * Callback khi thay đổi sắp xếp, dùng để gọi API
   */
  onSortChange?: (column: string | null, order: SortOrder) => void;

  /**
   * Cho phép chọn hàng
   */
  selectable?: boolean;

  /**
   * Cho phép mở rộng hàng
   */
  expandable?: boolean;

  /**
   * Cấu hình phân trang
   */
  pagination?: boolean | TablePaginationProps;

  /**
   * Cấu hình chọn hàng
   */
  rowSelection?: {
    selectedRowKeys: React.Key[];
    onChange: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
  };

  /**
   * Cấu hình mở rộng hàng
   */
  expandableConfig?: {
    expandedRowKeys?: React.Key[];
    onExpandedRowsChange?: (expandedRowKeys: React.Key[]) => void;
    expandedRowRender?: (record: T, index: number) => ReactNode;
    rowExpandable?: (record: T) => boolean;
  };

  /**
   * Kích thước bảng
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Có viền không
   */
  bordered?: boolean;

  /**
   * Có sọc không
   */
  striped?: boolean;

  /**
   * Có hover không
   */
  hoverable?: boolean;

  /**
   * Sự kiện onRow
   */
  onRow?: (record: T, index: number) => TableRowProps;

  /**
   * Sự kiện onHeaderRow
   */
  onHeaderRow?: (columns: TableColumn<T>[]) => TableRowProps;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties;

  /**
   * Khóa duy nhất cho mỗi hàng
   */
  rowKey?: string | ((record: T) => string);

  /**
   * Sort by default
   */
  defaultSort?: {
    column: string;
    order: SortOrder;
  };
}
