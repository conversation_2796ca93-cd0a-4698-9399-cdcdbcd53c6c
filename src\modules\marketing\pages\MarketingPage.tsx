import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import React from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Trang tổng quan Marketing
 */
const MarketingPage: React.FC = () => {
  const { t } = useTranslation('marketing');

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Audience Card */}
        <ModuleCard
          title={t('audience.title')}
          description={t('audience.description')}
          icon="users"
          linkTo="/marketing/audience"
        />

        {/* Segment Card */}
        <ModuleCard
          title={t('segment.title')}
          description={t('segment.description')}
          icon="filter"
          linkTo="/marketing/segment"
        />

        {/* Campaign Card */}
        <ModuleCard
          title={t('campaign.title')}
          description={t('campaign.description')}
          icon="campaign"
          linkTo="/marketing/campaign"
        />

        {/* Custom Fields Card */}
        <ModuleCard
          title={t('customFields.title', 'Quản lý trường tùy chỉnh')}
          description={t('customFields.description', 'Tạo và quản lý các trường dữ liệu tùy chỉnh')}
          icon="database"
          linkTo="/marketing/custom-fields"
        />

        {/* Tags Card */}
        <ModuleCard
          title={t('tags.title', 'Quản lý tag')}
          description={t('tags.description', 'Tạo và quản lý các tag cho khách hàng')}
          icon="tag"
          linkTo="/marketing/tags"
        />

        {/* Reports Card */}
        <ModuleCard
          title={t('reports.title', 'Báo cáo')}
          description={t('reports.description', 'Xem các báo cáo về hoạt động marketing')}
          icon="bar-chart"
          linkTo="/marketing/reports"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketingPage;
