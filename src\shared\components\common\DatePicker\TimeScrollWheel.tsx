import { useEffect, useRef, useState, useCallback } from 'react';

export interface TimeScrollWheelProps {
  /**
   * <PERSON><PERSON><PERSON> trị hiện tại
   */
  value: number;
  
  /**
   * Callback khi giá trị thay đổi
   */
  onChange: (value: number) => void;
  
  /**
   * <PERSON>h sách các giá trị có thể chọn
   */
  options: number[];
  
  /**
   * Label hiển thị
   */
  label: string;
  
  /**
   * Chiều cao của container
   */
  height?: number;
  
  /**
   * Chiều cao của mỗi item
   */
  itemHeight?: number;
  
  /**
   * Số lượng items hiển thị
   */
  visibleItems?: number;
}

/**
 * Component scroll wheel để chọn thời gian
 */
const TimeScrollWheel: React.FC<TimeScrollWheelProps> = ({
  value,
  onChange,
  options,
  label,
  height = 120,
  itemHeight = 32,
  visibleItems = 3,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [startScrollTop, setStartScrollTop] = useState(0);

  // Tính toán scroll position dựa trên value
  const getScrollPosition = useCallback((targetValue: number) => {
    const index = options.findIndex(option => option === targetValue);
    if (index === -1) return 0;
    
    const centerOffset = Math.floor(visibleItems / 2) * itemHeight;
    return index * itemHeight - centerOffset;
  }, [options, itemHeight, visibleItems]);

  // Tính toán value dựa trên scroll position
  const getValueFromScroll = useCallback((scrollTop: number) => {
    const centerOffset = Math.floor(visibleItems / 2) * itemHeight;
    const index = Math.round((scrollTop + centerOffset) / itemHeight);
    const clampedIndex = Math.max(0, Math.min(index, options.length - 1));
    return options[clampedIndex];
  }, [options, itemHeight, visibleItems]);

  // Scroll to value
  const scrollToValue = useCallback((targetValue: number, smooth = true) => {
    if (!containerRef.current) return;

    const scrollPosition = getScrollPosition(targetValue);
    containerRef.current.scrollTo({
      top: scrollPosition,
      behavior: smooth ? 'smooth' : 'auto',
    });
  }, [getScrollPosition]);

  // Initialize scroll position
  useEffect(() => {
    scrollToValue(value, false);
  }, [value, scrollToValue]);

  // Handle scroll with debouncing for smooth snapping
  const [scrollTimeout, setScrollTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleScroll = useCallback(() => {
    if (!containerRef.current || isDragging) return;

    const scrollTop = containerRef.current.scrollTop;
    const newValue = getValueFromScroll(scrollTop);

    if (newValue !== value) {
      onChange(newValue);
    }

    // Clear existing timeout
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // Set new timeout for snapping
    const timeout = setTimeout(() => {
      if (containerRef.current) {
        const currentScrollTop = containerRef.current.scrollTop;
        const snapValue = getValueFromScroll(currentScrollTop);
        scrollToValue(snapValue, true);
      }
    }, 150);

    setScrollTimeout(timeout);
  }, [value, onChange, getValueFromScroll, isDragging, scrollTimeout, scrollToValue]);

  // Handle mouse down
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setStartY(e.clientY);
    setStartScrollTop(containerRef.current?.scrollTop || 0);
    e.preventDefault();
  }, []);

  // Handle mouse move
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const deltaY = e.clientY - startY;
    const newScrollTop = startScrollTop - deltaY;
    containerRef.current.scrollTop = newScrollTop;
    
    e.preventDefault();
  }, [isDragging, startY, startScrollTop]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // Snap to nearest value
    if (containerRef.current) {
      const scrollTop = containerRef.current.scrollTop;
      const nearestValue = getValueFromScroll(scrollTop);
      scrollToValue(nearestValue, true);
      onChange(nearestValue);
    }
  }, [isDragging, getValueFromScroll, scrollToValue, onChange]);

  // Handle touch events
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
    setStartScrollTop(containerRef.current?.scrollTop || 0);
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const deltaY = e.touches[0].clientY - startY;
    const newScrollTop = startScrollTop - deltaY;
    containerRef.current.scrollTop = newScrollTop;
    
    e.preventDefault();
  }, [isDragging, startY, startScrollTop]);

  const handleTouchEnd = useCallback(() => {
    handleMouseUp();
  }, [handleMouseUp]);

  // Add global event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }

    // Return undefined for else case
    return undefined;
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Cleanup scroll timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [scrollTimeout]);

  // Generate padding items for smooth scrolling
  const paddingItems = Math.floor(visibleItems / 2);
  const paddedOptions = [
    ...Array(paddingItems).fill(null),
    ...options,
    ...Array(paddingItems).fill(null),
  ];

  return (
    <div className="flex flex-col items-center">
      <label className="block text-xs text-muted-foreground mb-2 text-center">
        {label}
      </label>
      
      <div className="relative">
        {/* Selection indicator */}
        <div
          className="absolute left-0 right-0 bg-primary/10 rounded pointer-events-none z-10"
          style={{
            top: `${paddingItems * itemHeight}px`,
            height: `${itemHeight}px`,
          }}
        />
        
        {/* Scroll container */}
        <div
          ref={containerRef}
          className="overflow-y-auto overflow-x-hidden select-none cursor-grab active:cursor-grabbing custom-scrollbar invisible-scrollbar"
          style={{ height: `${height}px`, width: '60px' }}
          onScroll={handleScroll}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onWheel={(e) => {
            // Enable mouse wheel scrolling
            e.stopPropagation();
          }}
        >
          <div className="flex flex-col">
            {paddedOptions.map((option, index) => (
              <div
                key={index}
                className={`
                  flex items-center justify-center text-sm transition-all duration-200 cursor-pointer
                  ${option === value ? 'text-primary font-medium' : 'text-muted-foreground hover:text-foreground'}
                  ${option === null ? 'opacity-0 pointer-events-none' : 'opacity-100'}
                  leading-none
                `}
                style={{
                  height: `${itemHeight}px`,
                  lineHeight: `${itemHeight}px`
                }}
                onClick={() => {
                  if (option !== null && option !== value) {
                    onChange(option);
                    scrollToValue(option, true);
                  }
                }}
              >
                {option !== null ? option.toString().padStart(2, '0') : ''}
              </div>
            ))}
          </div>
        </div>
        
        {/* Fade gradients */}
        <div className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-card to-transparent pointer-events-none" />
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-card to-transparent pointer-events-none" />
      </div>
    </div>
  );
};

export default TimeScrollWheel;
