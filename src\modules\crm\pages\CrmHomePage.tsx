import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid } from '@/shared/components/common';

/**
 * Trang chủ module CRM
 */
const CrmHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'crm']);

  // Danh sách các module con của CRM
  const subModules = [
    {
      id: 'customers',
      title: t('crm:modules.customers.title', 'Khách hàng'),
      description: t('crm:modules.customers.description', 'Quản lý thông tin khách hàng'),
      icon: 'users',
      count: 150,
      countLabel: t('crm:modules.customers.countLabel', 'Khách hàng'),
      linkTo: '/crm/customers',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'leads',
      title: t('crm:modules.leads.title', 'Tiềm năng'),
      description: t('crm:modules.leads.description', 'Quản lý khách hàng tiềm năng'),
      icon: 'user',
      count: 45,
      countLabel: t('crm:modules.leads.countLabel', 'Tiềm năng'),
      linkTo: '/crm/leads',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'deals',
      title: t('crm:modules.deals.title', 'Cơ hội'),
      description: t('crm:modules.deals.description', 'Quản lý cơ hội bán hàng'),
      icon: 'dollar-sign',
      count: 28,
      countLabel: t('crm:modules.deals.countLabel', 'Cơ hội'),
      linkTo: '/crm/deals',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'contacts',
      title: t('crm:modules.contacts.title', 'Liên hệ'),
      description: t('crm:modules.contacts.description', 'Quản lý thông tin liên hệ'),
      icon: 'phone',
      count: 210,
      countLabel: t('crm:modules.contacts.countLabel', 'Liên hệ'),
      linkTo: '/crm/contacts',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'activities',
      title: t('crm:modules.activities.title', 'Hoạt động'),
      description: t('crm:modules.activities.description', 'Quản lý hoạt động với khách hàng'),
      icon: 'calendar',
      count: 75,
      countLabel: t('crm:modules.activities.countLabel', 'Hoạt động'),
      linkTo: '/crm/activities',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'reports',
      title: t('crm:modules.reports.title', 'Báo cáo'),
      description: t('crm:modules.reports.description', 'Xem báo cáo và thống kê'),
      icon: 'chart',
      count: 12,
      countLabel: t('crm:modules.reports.countLabel', 'Báo cáo'),
      linkTo: '/crm/reports',
      linkText: t('common:view', 'Xem'),
    },
  ];

  return (
    <div>
      <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}>
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default CrmHomePage;
