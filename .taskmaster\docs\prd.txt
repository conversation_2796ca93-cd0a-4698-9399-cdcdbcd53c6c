# Product Requirements Document (PRD)
# Frontend Icon và Breadcrumb Enhancement Project

## Project Overview
Dự án cải tiến giao diện frontend cho hệ thống RedAI, tập trung vào việ<PERSON> bổ sung icon thiếu và cải thiện hệ thống breadcrumb navigation.

## Objectives
1. <PERSON><PERSON> sung các icon thiếu trong component Icon cho các module
2. <PERSON><PERSON>i thiện hệ thống ViewBreadcrumb với router mapping đầy đủ
3. <PERSON><PERSON><PERSON> bảo tính nhất quán trong navigation và UI

## Scope

### Phase 1: Icon Enhancement
- Phân tích các icon hiện có trong component Icon
- Xác định các icon thiếu cho modules: todolist, okrs, hrm, crm, marketing
- Bổ sung icon cho "dự án" (project), "Thống kê" (statistics), "Báo cáo" (reports)
- <PERSON><PERSON><PERSON> nhật TodolistHomePage với icon phù hợp

### Phase 2: Breadcrumb Router Mapping
- Rà soát tất cả router của các module
- Cập nhật ViewBreadcrumb với mapping đầy đủ
- Đảm bảo breadcrumb hoạt động chính xác cho tất cả routes

## Technical Requirements

### Icon Requirements
- Sử dụng Lucide React icons
- Maintain consistency với existing icon style
- Support dark/light theme
- Responsive sizing (xs, sm, md, lg, xl)

### Breadcrumb Requirements
- Dynamic breadcrumb generation based on current route
- Support for nested routes
- Translation key support
- Click navigation functionality
- Responsive design

## Modules to Cover

### Todolist Module
Routes:
- /todolist (home)
- /todolist/dashboard
- /todolist/tasks
- /todolist/tasks/:id
- /todolist/projects
- /todolist/projects/:id
- /todolist/statistics
- /todolist/statistics/enhanced

### OKRs Module
Routes:
- /okrs (home)
- /okrs/objectives
- /okrs/key-results
- /okrs/progress

### HRM Module
Routes:
- /hrm (home)
- /hrm/employees
- /hrm/departments
- /hrm/permissions

### CRM Module
Routes:
- /crm (home)
- /crm/customers
- /crm/leads
- /crm/opportunities

### Marketing Module
Routes:
- /marketing (home)
- /marketing/audience
- /marketing/segment
- /marketing/campaign
- /marketing/tags
- /marketing/custom-fields
- /marketing/reports

## Success Criteria
1. Tất cả ModuleCard hiển thị icon chính xác
2. ViewBreadcrumb hoạt động đúng cho tất cả routes
3. Navigation experience mượt mà và intuitive
4. Code maintainable và scalable

## Timeline
- Phase 1: Icon Enhancement (1-2 days)
- Phase 2: Breadcrumb Router Mapping (1-2 days)
- Testing và refinement (1 day)

## Dependencies
- Lucide React icon library
- React Router DOM
- i18next for translations
- Existing component architecture
