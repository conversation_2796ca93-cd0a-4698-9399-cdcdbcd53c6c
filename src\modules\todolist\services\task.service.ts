import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  TaskDto,
  TaskQueryDto,
  CreateTaskDto,
  UpdateTaskDto,
  UpdateTaskStatusDto,
} from '../types/task.types';

/**
 * Service cho công việc (task)
 */
export const TaskService = {
  /**
   * Lấy danh sách công việc
   * @param params Tham số truy vấn
   * @returns Promise với phản hồi API chứa danh sách công việc
   */
  getTasks: (params?: TaskQueryDto) => {
    return apiClient.get<PaginatedResult<TaskDto>>('/api/todos', { params });
  },

  /**
   * Lấy chi tiết công việc theo ID
   * @param id ID công việc
   * @returns Promise với phản hồi API chứa chi tiết công việc
   */
  getTask: (id: number) => {
    return apiClient.get<TaskDto>(`/api/todos/${id}`);
  },

  /**
   * <PERSON><PERSON>y danh sách công việc con
   * @param parentId ID công việc cha
   * @param params Tham số truy vấn
   * @returns Promise với phản hồi API chứa danh sách công việc con
   */
  getSubtasks: (parentId: number, params?: TaskQueryDto) => {
    return apiClient.get<PaginatedResult<TaskDto>>(`/api/todos/${parentId}/subtasks`, { params });
  },

  /**
   * Tạo công việc mới
   * @param data Dữ liệu tạo công việc
   * @returns Promise với phản hồi API chứa công việc đã tạo
   */
  createTask: (data: CreateTaskDto) => {
    return apiClient.post<TaskDto>('/api/todos', data);
  },

  /**
   * Tạo công việc con
   * @param parentId ID công việc cha
   * @param data Dữ liệu tạo công việc con
   * @returns Promise với phản hồi API chứa công việc con đã tạo
   */
  createSubtask: (parentId: number, data: CreateTaskDto) => {
    return apiClient.post<TaskDto>(`/api/todos/${parentId}/subtasks`, data);
  },

  /**
   * Cập nhật công việc
   * @param id ID công việc
   * @param data Dữ liệu cập nhật công việc
   * @returns Promise với phản hồi API chứa công việc đã cập nhật
   */
  updateTask: (id: number, data: UpdateTaskDto) => {
    return apiClient.patch<TaskDto>(`/api/todos/${id}`, data);
  },

  /**
   * Cập nhật trạng thái công việc
   * @param id ID công việc
   * @param data Dữ liệu cập nhật trạng thái
   * @returns Promise với phản hồi API chứa công việc đã cập nhật
   */
  updateTaskStatus: (id: number, data: UpdateTaskStatusDto) => {
    return apiClient.patch<TaskDto>(`/api/todos/${id}/status`, data);
  },

  /**
   * Xóa công việc
   * @param id ID công việc
   * @returns Promise với phản hồi API chứa kết quả xóa
   */
  deleteTask: (id: number) => {
    return apiClient.delete<boolean>(`/api/todos/${id}`);
  },
};
