import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { Breadcrumb, Icon } from '@/shared/components/common';

import type { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

interface ViewBreadcrumbProps {
  title: string;
  className?: string;
}

const ViewBreadcrumb: React.FC<ViewBreadcrumbProps> = ({ title, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  // Tạo breadcrumb items dựa trên đường dẫn hiện tại
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    // Luôn có item Home (hiển thị icon và text "Trang chủ")
    const items: BreadcrumbItem[] = [
      {
        label: '', // Hiển thị text "Trang chủ"
        path: '/',
        icon: <Icon name="home" size="sm" />,
        onClick: () => navigate('/'),
      },
    ];

    // Nếu không phải trang chủ, thêm trang hiện tại
    if (location.pathname !== '/') {
      // Xử lý các trường hợp đặc biệt
      if (location.pathname.startsWith('/components')) {
        // Xử lý các trang components
        if (location.pathname === '/components') {
          // Trang components chính
          items.push({
            label: 'components.library.title',
          });
        } else {
          // Các trang con của components
          items.push({
            label: 'components.library.title',
            path: '/components',
            onClick: () => navigate('/components'),
          });

          // Thêm trang hiện tại
          {
            // Kiểm tra xem title có tồn tại và có phải là key translation không
            const isTranslationKey =
              title && typeof title === 'string' && title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title || 'Trang chủ',
            });
          }
        }
      } else {
        // Xử lý các trang khác
        switch (location.pathname) {
          case '/animation-demo':
            items.push({
              label: 'common.animation',
            });
            break;
          case '/responsive-demo':
            items.push({
              label: 'common.components',
            });
            break;
          case '/ai-agents':
            items.push({
              label: 'chat.aiAgents',
            });
            break;
          case '/admin/rpoint/coupons/create':
            // Xử lý trang thêm mã khuyến mãi
            items.push({
              label: 'Mã Khuyến Mãi',
              path: '/admin/rpoint/coupons',
              onClick: () => navigate('/admin/rpoint/coupons'),
            });
            items.push({
              label: 'Thêm Mã Khuyến Mãi',
            });
            break;
          // Xử lý trang sửa mã khuyến mãi
          case location.pathname.includes('/admin/rpoint/coupons/edit/') && location.pathname:
            items.push({
              label: 'Mã Khuyến Mãi',
              path: '/admin/rpoint/coupons',
              onClick: () => navigate('/admin/rpoint/coupons'),
            });
            items.push({
              label: 'Sửa Mã Khuyến Mãi',
            });
            break;
          // Marketplace module
          case '/marketplace':
            items.push({
              label: 'marketplace:title',
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            break;
          case '/marketplace/cart':
            items.push({
              label: 'marketplace:title',
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: 'marketplace:cart.title',
            });
            break;
          case location.pathname.includes('/marketplace/product/') && location.pathname:
            items.push({
              label: 'marketplace:title',
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: 'marketplace:product.detail.information',
            });
            break;
          case location.pathname.includes('/marketplace/category/') && location.pathname:
            items.push({
              label: 'marketplace:title',
              path: '/marketplace',
              onClick: () => navigate('/marketplace'),
            });
            items.push({
              label: 'marketplace:categories',
            });
            break;

          // Marketing module
          case '/marketing':
            items.push({
              label: 'Marketing',
            });
            break;
          case '/marketing/audience':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Đối tượng',
            });
            break;
          case '/marketing/segment':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Phân đoạn',
            });
            break;
          case '/marketing/campaign':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Chiến dịch',
            });
            break;
          case '/marketing/tags':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Thẻ',
            });
            break;
          case '/marketing/custom-fields':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Trường tùy chỉnh',
            });
            break;
          case '/marketing/reports':
            items.push({
              label: 'Marketing',
              path: '/marketing',
              onClick: () => navigate('/marketing'),
            });
            items.push({
              label: 'Báo cáo',
            });
            break;

          // Todolist module
          case '/todolist':
            items.push({
              label: 'Danh sách công việc',
            });
            break;
          case '/todolist/dashboard':
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Dashboard',
            });
            break;
          case '/todolist/tasks':
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Công việc',
            });
            break;
          case location.pathname.includes('/todolist/tasks/') && location.pathname:
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Công việc',
              path: '/todolist/tasks',
              onClick: () => navigate('/todolist/tasks'),
            });
            items.push({
              label: 'Chi tiết công việc',
            });
            break;
          case '/todolist/projects':
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Dự án',
            });
            break;
          case location.pathname.includes('/todolist/projects/') && location.pathname:
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Dự án',
              path: '/todolist/projects',
              onClick: () => navigate('/todolist/projects'),
            });
            items.push({
              label: 'Chi tiết dự án',
            });
            break;
          case '/todolist/statistics':
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Thống kê',
            });
            break;
          case '/todolist/statistics/enhanced':
            items.push({
              label: 'Danh sách công việc',
              path: '/todolist',
              onClick: () => navigate('/todolist'),
            });
            items.push({
              label: 'Thống kê & Báo cáo',
            });
            break;

          // OKRs module
          case '/okrs':
            items.push({
              label: 'OKRs',
            });
            break;
          case '/okrs/cycles':
            items.push({
              label: 'OKRs',
              path: '/okrs',
              onClick: () => navigate('/okrs'),
            });
            items.push({
              label: 'Chu kỳ OKR',
            });
            break;
          case '/okrs/objectives':
            items.push({
              label: 'OKRs',
              path: '/okrs',
              onClick: () => navigate('/okrs'),
            });
            items.push({
              label: 'Mục tiêu',
            });
            break;

          // CRM module
          case '/crm':
            items.push({
              label: 'CRM',
            });
            break;

          // HRM module
          case '/hrm':
            items.push({
              label: 'hrm:title',
            });
            break;
          case '/hrm/employees':
            items.push({
              label: 'hrm:title',
              path: '/hrm',
              onClick: () => navigate('/hrm'),
            });
            items.push({
              label: 'hrm:employee.title',
            });
            break;
          case '/hrm/departments':
            items.push({
              label: 'hrm:title',
              path: '/hrm',
              onClick: () => navigate('/hrm'),
            });
            items.push({
              label: 'hrm:modules.departments.title',
            });
            break;
          case '/hrm/permissions':
            items.push({
              label: 'hrm:title',
              path: '/hrm',
              onClick: () => navigate('/hrm'),
            });
            items.push({
              label: 'hrm:permission.title',
            });
            break;
          case '/hrm/employees/create-user':
            items.push({
              label: 'hrm:title',
              path: '/hrm',
              onClick: () => navigate('/hrm'),
            });
            items.push({
              label: 'hrm:employee.title',
              path: '/hrm/employees',
              onClick: () => navigate('/hrm/employees'),
            });
            items.push({
              label: 'Tạo tài khoản người dùng',
            });
            break;
          case location.pathname.includes('/hrm/employees/create-user/') && location.pathname:
            items.push({
              label: 'hrm:title',
              path: '/hrm',
              onClick: () => navigate('/hrm'),
            });
            items.push({
              label: 'hrm:employee.title',
              path: '/hrm/employees',
              onClick: () => navigate('/hrm/employees'),
            });
            items.push({
              label: 'Tạo tài khoản người dùng cho nhân viên',
            });
            break;
          default: {
            // Nếu không có xử lý đặc biệt, sử dụng title được truyền vào
            // Kiểm tra xem title có tồn tại và có phải là key translation không
            const isTranslationKey =
              title && typeof title === 'string' && title.includes(':') && !title.includes(' ');
            items.push({
              label: isTranslationKey ? t(title) : title || 'Trang chủ',
            });
          }
        }
      }
    }

    return items;
  };

  return (
    <div className="flex items-center overflow-hidden">
      <Breadcrumb items={generateBreadcrumbItems()} className={`text-sm ${className} truncate`} />
      {/* Hiệu ứng ánh sáng nhẹ */}
      <div className="absolute w-8 h-8 bg-primary opacity-10 rounded-full blur-xl -z-10 animate-pulse-slow"></div>
    </div>
  );
};

export default ViewBreadcrumb;
