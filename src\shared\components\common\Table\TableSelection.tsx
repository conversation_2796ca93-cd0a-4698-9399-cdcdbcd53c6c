import React from 'react';

import { Checkbox } from '@/shared/components/common';

interface TableSelectionProps {
  /**
   * Đ<PERSON> chọn hay chưa
   */
  checked: boolean;

  /**
   * Đ<PERSON> chọn một phần hay chưa
   */
  indeterminate?: boolean;

  /**
   * Đ<PERSON> vô hiệu hóa hay chưa
   */
  disabled?: boolean;

  /**
   * Callback khi thay đổi
   */
  onChange: (checked: boolean) => void;

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Component chọn hàng
 */
const TableSelection: React.FC<TableSelectionProps> = ({
  checked,
  indeterminate = false,
  disabled = false,
  onChange,
  className = '',
}) => {
  // Xử lý sự kiện thay đổi
  const handleChange = (checked: boolean) => {
    onChange(checked);
  };

  return (
    <Checkbox
      checked={checked}
      indeterminate={indeterminate}
      disabled={disabled}
      variant="filled"
      onChange={handleChange}
      className={className}
    />
  );
};

export default TableSelection;
