import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import {
  Card,
  Button,
  Typography,
  Select,
  RangePicker,
  ResponsiveGrid,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';

// import TaskPriorityChart from '../components/charts/TaskPriorityChart';
import CompletionRateChart from '../components/charts/CompletionRateChart';
import TaskStatusChart from '../components/charts/TaskStatusChart';
import RecentTasksList from '../components/RecentTasksList';
import { useProjects } from '../hooks/useProjects';
import { useStatisticsOverview, useUserPerformance } from '../hooks/useStatistics';
// import { useAuth } from '@/shared/hooks/useAuth';
import { useTasks } from '../hooks/useTasks';
import { TaskStatus } from '../types/task.types';

/**
 * Dashboard page for todolist module
 */
const DashboardPage: React.FC = () => {
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();
  // const { user } = useAuth();
  const user = { id: 1 }; // Mock user for now
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');

  // Convert date range to timestamps
  const startDate = dateRange[0] ? dateRange[0].getTime() : undefined;
  const endDate = dateRange[1] ? dateRange[1].getTime() : undefined;

  // Fetch tasks with limit
  const { data: tasksData, isLoading: isLoadingTasks } = useTasks({
    limit: 5,
    sortBy: 'createdAt',
    sortDirection: 'DESC',
  });

  // Fetch projects
  const { data: projectsData, isLoading: isLoadingProjects } = useProjects();

  // Fetch statistics overview
  useStatisticsOverview(startDate, endDate);

  // Fetch user performance
  const { data: userPerformance, isLoading: isLoadingUserPerformance } = useUserPerformance(
    user?.id || 0,
    startDate,
    endDate,
    selectedProjectId ? parseInt(selectedProjectId, 10) : undefined
  );

  // Handle date range change
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
  };

  // Handle project change
  const handleProjectChange = (value: string | number | string[] | number[]) => {
    setSelectedProjectId(value as string);
  };

  // Navigate to task list
  const handleViewAllTasks = () => {
    navigate('/todolist/tasks');
  };

  // Navigate to project list
  const handleViewAllProjects = () => {
    navigate('/todolist/projects');
  };

  // Navigate to statistics
  const handleViewStatistics = () => {
    navigate('/todolist/statistics');
  };

  // Calculate task statistics
  const calculateTaskStats = () => {
    if (!tasksData?.items) {return { total: 0, pending: 0, inProgress: 0, completed: 0 };}

    const total = tasksData.items.length;
    const pending = tasksData.items.filter(task => task.status === TaskStatus.PENDING).length;
    const inProgress = tasksData.items.filter(
      task => task.status === TaskStatus.IN_PROGRESS
    ).length;
    const completed = tasksData.items.filter(
      task => task.status === TaskStatus.COMPLETED || task.status === TaskStatus.APPROVED
    ).length;

    return { total, pending, inProgress, completed };
  };

  // Calculate task statistics
  calculateTaskStats();

  return (
    <div>
      <div className="mb-6">
        <Typography variant="h3" className="mb-2">
          {t('todolist:dashboard.title', 'Dashboard')}
        </Typography>
        <Typography variant="body1" className="text-gray-500">
          {t('todolist:dashboard.description', 'Overview of your tasks and projects')}
        </Typography>
      </div>

      <div className="mb-6">
        <Card className="p-4">
          <div className="flex flex-wrap gap-4 justify-between items-center">
            <div className="flex flex-wrap gap-4">
              <div className="w-full md:w-64">
                <Select
                  placeholder={t('todolist:dashboard.filters.selectProject', 'All Projects')}
                  onChange={handleProjectChange}
                  value={selectedProjectId}
                  options={[
                    {
                      value: '',
                      label: t('todolist:dashboard.filters.allProjects', 'All Projects'),
                    },
                    ...(projectsData?.items?.map(project => ({
                      value: project.id.toString(),
                      label: project.title,
                    })) || []),
                  ]}
                  loading={isLoadingProjects}
                />
              </div>
              <div className="w-full md:w-auto">
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  placeholder={[
                    t('todolist:dashboard.filters.startDate', 'Start Date'),
                    t('todolist:dashboard.filters.endDate', 'End Date'),
                  ]}
                />
              </div>
            </div>
            <Button onClick={handleViewStatistics}>
              {t('todolist:dashboard.viewDetailedStats', 'View Detailed Statistics')}
            </Button>
          </div>
        </Card>
      </div>

      <div className="mb-6">
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4 }} gap={6}>
          <Card className="h-full">
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:dashboard.stats.totalTasks', 'Total Tasks')}
              </Typography>
              <div className="flex items-end justify-between">
                <Typography variant="h3" className="font-bold">
                  {isLoadingTasks ? (
                    <div className="text-sm text-gray-500">Loading...</div>
                  ) : (
                    tasksData?.meta.totalItems || 0
                  )}
                </Typography>
                <div className="text-gray-500 text-sm">
                  {t('todolist:dashboard.stats.inSystem', 'in system')}
                </div>
              </div>
            </div>
          </Card>
          <Card className="h-full">
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:dashboard.stats.pendingTasks', 'Pending Tasks')}
              </Typography>
              <div className="flex items-end justify-between">
                <Typography variant="h3" className="font-bold text-yellow-500">
                  {isLoadingUserPerformance ? (
                    <div className="text-sm text-gray-500">Loading...</div>
                  ) : (
                    userPerformance?.pendingTasks || 0
                  )}
                </Typography>
                <div className="text-gray-500 text-sm">
                  {t('todolist:dashboard.stats.needAttention', 'need attention')}
                </div>
              </div>
            </div>
          </Card>
          <Card className="h-full">
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:dashboard.stats.completionRate', 'Completion Rate')}
              </Typography>
              <div className="flex items-end justify-between">
                <Typography variant="h3" className="font-bold text-green-500">
                  {isLoadingUserPerformance ? (
                    <div className="text-sm text-gray-500">Loading...</div>
                  ) : (
                    `${(userPerformance?.completionRate || 0).toFixed(1)}%`
                  )}
                </Typography>
                <div className="text-gray-500 text-sm">
                  {t('todolist:dashboard.stats.efficiency', 'efficiency')}
                </div>
              </div>
            </div>
          </Card>
          <Card className="h-full">
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:dashboard.stats.projects', 'Projects')}
              </Typography>
              <div className="flex items-end justify-between">
                <Typography variant="h3" className="font-bold text-blue-500">
                  {isLoadingProjects ? (
                    <div className="text-sm text-gray-500">Loading...</div>
                  ) : (
                    projectsData?.meta.totalItems || 0
                  )}
                </Typography>
                <div className="text-gray-500 text-sm">
                  {t('todolist:dashboard.stats.active', 'active')}
                </div>
              </div>
            </div>
          </Card>
        </ResponsiveGrid>
      </div>

      <div className="mb-6">
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 1, lg: 2 }} gap={6}>
          <Card className="h-full">
            <div className="p-4">
              <div className="flex justify-between items-center mb-4">
                <Typography variant="h6">
                  {t('todolist:dashboard.charts.taskDistribution', 'Task Distribution')}
                </Typography>
                <Select
                  value="status"
                  options={[
                    {
                      value: 'status',
                      label: t('todolist:dashboard.charts.byStatus', 'By Status'),
                    },
                    {
                      value: 'priority',
                      label: t('todolist:dashboard.charts.byPriority', 'By Priority'),
                    },
                  ]}
                  className="w-40"
                />
              </div>
              <div className="h-64">
                {isLoadingTasks ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
                  </div>
                ) : (
                  <TaskStatusChart tasks={tasksData?.items || []} />
                )}
              </div>
            </div>
          </Card>
          <Card className="h-full">
            <div className="p-4">
              <Typography variant="h6" className="mb-4">
                {t('todolist:dashboard.charts.completionRate', 'Completion Rate')}
              </Typography>
              <div className="h-64 flex items-center justify-center">
                {isLoadingUserPerformance ? (
                  <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
                ) : (
                  <CompletionRateChart rate={userPerformance?.completionRate || 0} />
                )}
              </div>
            </div>
          </Card>
        </ResponsiveGrid>
      </div>

      <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 1, lg: 2 }} gap={6}>
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Typography variant="h6">
                {t('todolist:dashboard.recentTasks', 'Recent Tasks')}
              </Typography>
              <Button variant="outline" onClick={handleViewAllTasks}>
                {t('todolist:dashboard.viewAll', 'View All')}
              </Button>
            </div>
            <RecentTasksList tasks={tasksData?.items || []} isLoading={isLoadingTasks} />
          </div>
        </Card>
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <Typography variant="h6">{t('todolist:dashboard.projects', 'Projects')}</Typography>
              <Button variant="outline" onClick={handleViewAllProjects}>
                {t('todolist:dashboard.viewAll', 'View All')}
              </Button>
            </div>
            {isLoadingProjects ? (
              <div className="h-64 flex items-center justify-center">
                <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
              </div>
            ) : projectsData?.items && projectsData.items.length > 0 ? (
              <div className="space-y-4">
                {projectsData.items.slice(0, 5).map(project => (
                  <div
                    key={project.id}
                    className="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer"
                    onClick={() => navigate(`/todolist/projects/${project.id}`)}
                  >
                    <div className="flex justify-between items-center">
                      <Typography variant="subtitle1" className="font-medium">
                        {project.title}
                      </Typography>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          project.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {project.isActive
                          ? t('todolist:project.status.active', 'Active')
                          : t('todolist:project.status.inactive', 'Inactive')}
                      </div>
                    </div>
                    {project.description && (
                      <Typography variant="body2" className="text-gray-500 mt-1 line-clamp-1">
                        {project.description}
                      </Typography>
                    )}
                    <div className="text-xs text-gray-500 mt-2">
                      {t('todolist:project.fields.createdAt', 'Created')}:{' '}
                      {formatDate(project.createdAt || 0)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                {t('todolist:dashboard.noProjects', 'No projects found')}
              </div>
            )}
          </div>
        </Card>
      </ResponsiveGrid>
    </div>
  );
};

export default DashboardPage;
