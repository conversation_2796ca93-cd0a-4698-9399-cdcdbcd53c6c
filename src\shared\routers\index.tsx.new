import { affiliateRoutes } from '@/modules/admin/affiliate';
import { authRoutes } from '@/modules/auth';
import calendarRoutes from '@/modules/calendar/routers/calendarRoutes';
import crmRoutes from '@/modules/crm/routers/crmRoutes';
import homeRoutes from '@/modules/home/<USER>/homeRoutes';
import hrmRoutes from '@/modules/hrm/routers/hrmRoutes';
import marketingRoutes from '@/modules/marketing/routers/marketingRoutes';
import okrsRoutes from '@/modules/okrs/routers/okrsRoutes';
import { productRoutes } from '@/modules/product';
import todolistRoutes from '@/modules/todolist/routers/todolistRoutes';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import componentRoutes from './modules/componentRoutes';

/**
 * Create router with all routes from modules
 */
const router = createBrowserRouter([
  // Routes không yêu cầu đăng nhập
  ...authRoutes,

  // Routes yêu cầu đăng nhập
  ...homeRoutes,
  ...todolistRoutes,
  ...okrsRoutes,
  ...marketingRoutes,
  ...crmRoutes,
  ...hrmRoutes,
  ...calendarRoutes,
  ...productRoutes,

  // Routes khác
  ...componentRoutes,
  ...affiliateRoutes,
]);

const AppRouter = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;
