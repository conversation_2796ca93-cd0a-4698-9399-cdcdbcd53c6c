import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { ResponsiveGrid } from '@/shared/components/common';

/**
 * Trang chủ module Marketing
 */
const MarketingHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'marketing']);

  // Danh sách các module con của Marketing
  const subModules = [
    {
      id: 'campaigns',
      title: t('marketing:modules.campaigns.title', 'Chiến dịch'),
      description: t('marketing:modules.campaigns.description', 'Quản lý các chiến dịch marketing'),
      icon: 'campaign',
      count: 5,
      countLabel: t('marketing:modules.campaigns.countLabel', 'Chiến dịch'),
      linkTo: '/marketing/campaigns',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'content',
      title: t('marketing:modules.content.title', 'Nội dung'),
      description: t('marketing:modules.content.description', 'Quản lý nội dung marketing'),
      icon: 'document',
      count: 12,
      countLabel: t('marketing:modules.content.countLabel', 'Nội dung'),
      linkTo: '/marketing/content',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'email',
      title: t('marketing:modules.email.title', 'Email Marketing'),
      description: t('marketing:modules.email.description', 'Quản lý chiến dịch email marketing'),
      icon: 'mail',
      count: 8,
      countLabel: t('marketing:modules.email.countLabel', 'Chiến dịch'),
      linkTo: '/marketing/email',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'social',
      title: t('marketing:modules.social.title', 'Mạng xã hội'),
      description: t('marketing:modules.social.description', 'Quản lý marketing trên mạng xã hội'),
      icon: 'share-2',
      count: 3,
      countLabel: t('marketing:modules.social.countLabel', 'Kênh'),
      linkTo: '/marketing/social',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'analytics',
      title: t('marketing:modules.analytics.title', 'Phân tích'),
      description: t('marketing:modules.analytics.description', 'Phân tích hiệu quả marketing'),
      icon: 'chart',
      count: 4,
      countLabel: t('marketing:modules.analytics.countLabel', 'Báo cáo'),
      linkTo: '/marketing/analytics',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'automation',
      title: t('marketing:modules.automation.title', 'Tự động hóa'),
      description: t('marketing:modules.automation.description', 'Tự động hóa quy trình marketing'),
      icon: 'refresh',
      count: 6,
      countLabel: t('marketing:modules.automation.countLabel', 'Quy trình'),
      linkTo: '/marketing/automation',
      linkText: t('common:view', 'Xem'),
    },
  ];

  return (
    <div>
      <ResponsiveGrid gap={4} maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}>
        {subModules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default MarketingHomePage;
