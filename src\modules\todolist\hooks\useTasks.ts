import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { TaskService } from '../services/task.service';
import {
  TaskQueryDto,
  CreateTaskDto,
  UpdateTaskDto,
  UpdateTaskStatusDto,
} from '../types/task.types';

// Key cho React Query
const TASKS_QUERY_KEY = 'tasks';

/**
 * Hook để lấy danh sách công việc
 * @param params Tham số truy vấn
 * @returns Query result với danh sách công việc
 */
export const useTasks = (params?: TaskQueryDto) => {
  return useQuery({
    queryKey: [TASKS_QUERY_KEY, params],
    queryFn: () => TaskService.getTasks(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết công việc
 * @param id ID công việc
 * @returns Query result với chi tiết công việc
 */
export const useTask = (id: number) => {
  return useQuery({
    queryKey: [TASKS_QUERY_KEY, id],
    queryFn: () => TaskService.getTask(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để lấy danh sách công việc con
 * @param parentId ID công việc cha
 * @param params Tham số truy vấn
 * @returns Query result với danh sách công việc con
 */
export const useSubtasks = (parentId: number, params?: TaskQueryDto) => {
  return useQuery({
    queryKey: [TASKS_QUERY_KEY, 'subtasks', parentId, params],
    queryFn: () => TaskService.getSubtasks(parentId, params),
    select: data => data.result,
    enabled: !!parentId,
  });
};

/**
 * Hook để lấy danh sách công việc của một dự án
 * @param projectId ID dự án
 * @param params Tham số truy vấn
 * @returns Query result với danh sách công việc của dự án
 */
export const useProjectTasks = (projectId: number, params?: TaskQueryDto) => {
  const queryParams = { ...params, categoryId: projectId };
  return useQuery({
    queryKey: [TASKS_QUERY_KEY, 'project', projectId, params],
    queryFn: () => TaskService.getTasks(queryParams),
    select: data => data.result,
    enabled: !!projectId,
  });
};

/**
 * Hook để tạo công việc mới
 * @returns Mutation result cho việc tạo công việc
 */
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTaskDto) => TaskService.createTask(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để tạo công việc con
 * @returns Mutation result cho việc tạo công việc con
 */
export const useCreateSubtask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ parentId, data }: { parentId: number; data: CreateTaskDto }) =>
      TaskService.createSubtask(parentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [TASKS_QUERY_KEY, 'subtasks', variables.parentId],
      });
    },
  });
};

/**
 * Hook để cập nhật công việc
 * @returns Mutation result cho việc cập nhật công việc
 */
export const useUpdateTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTaskDto }) =>
      TaskService.updateTask(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để cập nhật trạng thái công việc
 * @returns Mutation result cho việc cập nhật trạng thái công việc
 */
export const useUpdateTaskStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTaskStatusDto }) =>
      TaskService.updateTaskStatus(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY, variables.id] });
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY, 'subtasks'] });
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY, 'project'] });
    },
  });
};

/**
 * Hook để xóa công việc
 * @returns Mutation result cho việc xóa công việc
 */
export const useDeleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => TaskService.deleteTask(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [TASKS_QUERY_KEY] });
    },
  });
};
