import React from 'react';

export interface TableLoadingProps {
  /**
   * Loại loading
   * @default 'overlay'
   */
  type?: 'overlay' | 'inline' | 'skeleton';

  /**
   * Số hàng skeleton hiển thị
   * @default 5
   */
  skeletonRows?: number;

  /**
   * Số cột skeleton hiển thị
   * @default 4
   */
  skeletonColumns?: number;

  /**
   * <PERSON><PERSON>ch thước của loading spinner
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * <PERSON><PERSON><PERSON> của loading spinner
   * @default 'primary'
   */
  color?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';

  /**
   * Text hiển thị khi loading
   */
  loadingText?: string;
}

/**
 * Component hiển thị trạng thái loading cho bảng
 */
const TableLoading: React.FC<TableLoadingProps> = ({
  type = 'overlay',
  skeletonRows = 5,
  skeletonColumns = 4,
  size = 'md',
  color = 'primary',
  loadingText,
}) => {
  // <PERSON><PERSON>ch thước spinner
  const spinnerSize = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  }[size];

  // Màu spinner
  const spinnerColor = {
    primary: 'border-primary',
    secondary: 'border-gray-500',
    success: 'border-green-500',
    danger: 'border-red-500',
    warning: 'border-yellow-500',
    info: 'border-blue-500',
  }[color];

  // Spinner component
  const Spinner = () => (
    <div
      className={`animate-spin rounded-full ${spinnerSize} border-t-2 border-b-2 ${spinnerColor}`}
    ></div>
  );

  // Skeleton row
  const SkeletonRow = ({ columns }: { columns: number }) => (
    <tr>
      {Array.from({ length: columns }).map((_, index) => (
        <td key={index} className="px-4 py-2">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </td>
      ))}
    </tr>
  );

  // Skeleton loading
  if (type === 'skeleton') {
    return (
      <tbody className="animate-pulse">
        {Array.from({ length: skeletonRows }).map((_, index) => (
          <SkeletonRow key={index} columns={skeletonColumns} />
        ))}
      </tbody>
    );
  }

  // Inline loading
  if (type === 'inline') {
    return (
      <tr>
        <td colSpan={skeletonColumns} className="text-center py-8">
          <div className="flex flex-col items-center justify-center">
            <Spinner />
            {loadingText && (
              <div className="mt-2 text-gray-500 dark:text-gray-400">{loadingText}</div>
            )}
          </div>
        </td>
      </tr>
    );
  }

  // Overlay loading (default)
  return (
    <div className="absolute inset-0 bg-white/70 dark:bg-gray-900/70 flex items-center justify-center z-10">
      <div className="flex flex-col items-center">
        <Spinner />
        {loadingText && <div className="mt-2 text-gray-700 dark:text-gray-300">{loadingText}</div>}
      </div>
    </div>
  );
};

export default TableLoading;
