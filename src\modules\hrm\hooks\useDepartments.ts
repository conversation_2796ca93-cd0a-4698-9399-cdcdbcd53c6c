import { SearchItem } from '@/shared/types/search-input-with-image.types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { DepartmentService } from '../services/department.service';
import {
  CreateDepartmentDto,
  DepartmentDto,
  DepartmentQueryDto,
  UpdateDepartmentDto,
} from '../types/department.types';

// Key cho React Query
const DEPARTMENTS_QUERY_KEY = 'departments';

/**
 * Hook để lấy danh sách phòng ban
 * @param params Tham số truy vấn
 * @returns Query result với danh sách phòng ban
 */
export const useDepartments = (params?: DepartmentQueryDto) => {
  return useQuery({
    queryKey: [DEPARTMENTS_QUERY_KEY, params],
    queryFn: () => DepartmentService.getDepartments(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy chi tiết phòng ban
 * @param id ID phòng ban
 * @returns Query result với chi tiết phòng ban
 */
export const useDepartment = (id: number) => {
  return useQuery({
    queryKey: [DEPARTMENTS_QUERY_KEY, id],
    queryFn: () => DepartmentService.getDepartment(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để lấy cấu trúc cây phòng ban
 * @returns Query result với cấu trúc cây phòng ban
 */
export const useDepartmentTree = () => {
  return useQuery({
    queryKey: [DEPARTMENTS_QUERY_KEY, 'tree'],
    queryFn: () => DepartmentService.getDepartmentTree(),
    select: data => data.result,
  });
};

/**
 * Hook để lấy danh sách thành viên trong phòng ban
 * @param id ID phòng ban
 * @returns Query result với danh sách thành viên
 */
export const useDepartmentMembers = (id: number) => {
  return useQuery({
    queryKey: [DEPARTMENTS_QUERY_KEY, id, 'members'],
    queryFn: () => DepartmentService.getDepartmentMembers(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo phòng ban mới
 * @returns Mutation result cho việc tạo phòng ban
 */
export const useCreateDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateDepartmentDto) => DepartmentService.createDepartment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật phòng ban
 * @returns Mutation result cho việc cập nhật phòng ban
 */
export const useUpdateDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateDepartmentDto }) =>
      DepartmentService.updateDepartment(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY, variables.id] });
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY, 'tree'] });
    },
  });
};

/**
 * Hook để xóa phòng ban
 * @returns Mutation result cho việc xóa phòng ban
 */
export const useDeleteDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => DepartmentService.deleteDepartment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [DEPARTMENTS_QUERY_KEY, 'tree'] });
    },
  });
};

/**
 * Interface cho kết quả phân trang
 */
interface PaginatedSearchResult<T> {
  items: T[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho tham số phân trang
 */
interface PaginationParams {
  page: number;
  limit: number;
}

/**
 * Hàm tìm kiếm phòng ban với lazy loading và hỗ trợ phân trang
 * @param searchTerm Từ khóa tìm kiếm
 * @param pagination Tham số phân trang (tùy chọn)
 * @returns Promise với danh sách phòng ban dạng SearchItem hoặc kết quả phân trang
 */
export const searchDepartments = async (
  searchTerm: string,
  pagination?: PaginationParams
): Promise<SearchItem[] | PaginatedSearchResult<SearchItem>> => {
  try {
    // Nếu không có từ khóa tìm kiếm và không yêu cầu tải mặc định, trả về mảng rỗng
    if (!searchTerm.trim() && !pagination) {
      return [];
    }

    // Gọi API tìm kiếm phòng ban với phân trang
    const response = await DepartmentService.getDepartments({
      search: searchTerm.trim() || undefined,
      page: pagination?.page || 1,
      limit: pagination?.limit || 10,
    });

    // Chuyển đổi kết quả sang định dạng SearchItem
    const items = response.result.items.map((department: DepartmentDto) => ({
      id: department.id,
      name: department.name,
      description: department.description || undefined,
      // Có thể thêm icon nếu không có hình ảnh
      icon: undefined,
    }));

    // Nếu có tham số phân trang, trả về kết quả phân trang
    if (pagination) {
      return {
        items,
        meta: response.result.meta,
      };
    }

    // Nếu không có tham số phân trang, trả về mảng items
    return items;
  } catch (error) {
    console.error('Error searching departments:', error);
    return [];
  }
};

/**
 * Hàm load phòng ban cho AsyncSelectWithPagination
 * @param params Tham số tìm kiếm và phân trang
 * @returns Promise với kết quả phân trang SelectOption
 */
export const loadDepartmentsForAsyncSelect = async (params: {
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{
  items: SelectOption[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}> => {
  try {
    const { search = '', page = 1, limit = 20 } = params;

    // Gọi API tìm kiếm phòng ban với phân trang
    const response = await DepartmentService.getDepartments({
      search: search.trim() || undefined,
      page,
      limit,
    });

    // Chuyển đổi kết quả sang định dạng SelectOption
    const items: SelectOption[] = response.result.items.map((department: DepartmentDto) => ({
      value: department.id,
      label: department.name,
      data: {
        description: department.description,
        parentId: department.parentId,
        managerId: department.managerId,
      },
    }));

    return {
      items,
      totalItems: response.result.meta.totalItems,
      totalPages: response.result.meta.totalPages,
      currentPage: response.result.meta.currentPage,
    };
  } catch (error) {
    console.error('Error loading departments for AsyncSelect:', error);
    return {
      items: [],
      totalItems: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
};
