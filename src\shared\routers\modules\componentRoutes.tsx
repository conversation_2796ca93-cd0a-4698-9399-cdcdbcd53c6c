import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

import AIComponentsPage from '@/modules/components/pages/AIComponentsPage';
import AnalyticsComponentsPage from '@/modules/components/pages/AnalyticsComponentsPage';
import AvatarPage from '@/modules/components/pages/AvatarPage';
import BannerPage from '@/modules/components/pages/BannerPage';
import CardComponentsPage from '@/modules/components/pages/CardComponentsPage';
import ChatComponentsPage from '@/modules/components/pages/ChatComponentsPage';
import ImageGalleryPage from '@/modules/components/pages/ImageGalleryPage';
import KanbanBoardPage from '@/modules/components/pages/KanbanBoardPage';
import MarketingComponentsPage from '@/modules/components/pages/MarketingComponentsPage';
import NotificationPage from '@/modules/components/pages/NotificationPage';
import SearchInputWithImagePage from '@/modules/components/pages/SearchInputWithImagePage';
import TablePage from '@/modules/components/pages/TablePage';
import TagForm from '@/modules/components/pages/TagForm';
import TopCardPage from '@/modules/components/pages/TopCardPage';
import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';

// Import Components module pages
const ComponentsPage = lazy(() => import('@/modules/components/pages/ComponentsPage'));
const AnimationPage = lazy(() => import('@/modules/components/pages/AnimationPage'));
const ButtonsPage = lazy(() => import('@/modules/components/pages/ButtonsPage'));
const CardsPage = lazy(() => import('@/modules/components/pages/CardsPage'));
const ChipsPage = lazy(() => import('@/modules/components/pages/ChipsPage'));
const DeleteConfirmModalExample = lazy(
  () => import('@/modules/components/pages/DeleteConfirmModalExample')
);
const InputsPage = lazy(() => import('@/modules/components/pages/InputsPage'));
const LayoutComponentsPage = lazy(() => import('@/modules/components/pages/LayoutComponentsPage'));
const ThemeComponentsPage = lazy(() => import('@/modules/components/pages/ThemeComponentsPage'));
const ThemeSystemPage = lazy(() => import('@/modules/components/pages/ThemeSystemPage'));
const GridPage = lazy(() => import('@/modules/components/pages/GridPage'));
const TypographyPage = lazy(() => import('@/modules/components/pages/TypographyPage'));
const MenuPage = lazy(() => import('@/modules/components/pages/MenuPage'));
const ModernTooltipPage = lazy(() => import('@/modules/components/pages/ModernTooltipPage'));
const SearchBarPage = lazy(() => import('@/modules/components/pages/SearchBarPage'));
const ModernMenuPage = lazy(() => import('@/modules/components/pages/ModernMenuPage'));
const ResponsiveGridPage = lazy(() => import('@/modules/components/pages/ResponsiveGridPage'));
const LineChartPage = lazy(() => import('@/modules/components/pages/charts/LineChartPage'));
const SimpleChartPage = lazy(() => import('@/modules/components/pages/charts/SimpleChartPage'));
const ChartDemoPage = lazy(() => import('@/modules/components/pages/charts/ChartDemoPage'));
const FormWizardPage = lazy(() => import('@/modules/components/pages/form/wizard/FormWizardPage'));
const FlowNodesPage = lazy(() => import('@/modules/components/pages/flow/FlowNodesPage'));

// Import Components demo pages
const LoadingButtonDemo = lazy(() => import('@/modules/components/buttons/LoadingButtonDemo'));
const ThemeFormDemo = lazy(() => import('@/modules/components/pages/form/ThemeFormDemo'));

// Import DataDisplayPage
import DataDisplayPage from '@/modules/components/pages/DataDisplayPage';
import { ProductComponentsDemo, ProductPagesDemo } from '@/modules/components/pages/product';

/**
 * Components module routes
 */
const componentRoutes: RouteObject[] = [
  {
    path: '/components',
    element: (
      <MainLayout title="Components">
        <Suspense fallback={<Loading />}>
          <ComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/animation',
    element: (
      <MainLayout title="Animation">
        <Suspense fallback={<Loading />}>
          <AnimationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/buttons',
    element: (
      <MainLayout title="Buttons">
        <Suspense fallback={<Loading />}>
          <ButtonsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/cards',
    element: (
      <MainLayout title="Cards">
        <Suspense fallback={<Loading />}>
          <CardsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/chips',
    element: (
      <MainLayout title="Chips">
        <Suspense fallback={<Loading />}>
          <ChipsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/delete-confirm-modal',
    element: (
      <MainLayout title="Delete Confirmation Modal">
        <Suspense fallback={<Loading />}>
          <DeleteConfirmModalExample />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/inputs',
    element: (
      <MainLayout title="Inputs">
        <Suspense fallback={<Loading />}>
          <InputsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/layout',
    element: (
      <MainLayout title="Layout Components">
        <Suspense fallback={<Loading />}>
          <LayoutComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/theme',
    element: (
      <MainLayout title="Theme Components">
        <Suspense fallback={<Loading />}>
          <ThemeComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/theme-system',
    element: (
      <MainLayout title="Theme System">
        <Suspense fallback={<Loading />}>
          <ThemeSystemPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/grid',
    element: (
      <MainLayout title="Grid System">
        <Suspense fallback={<Loading />}>
          <GridPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/typography',
    element: (
      <MainLayout title="Typography">
        <Suspense fallback={<Loading />}>
          <TypographyPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/menu',
    element: (
      <MainLayout title="Menu">
        <Suspense fallback={<Loading />}>
          <MenuPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/tooltip',
    element: (
      <MainLayout title="Tooltip">
        <Suspense fallback={<Loading />}>
          <ModernTooltipPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/search-bar',
    element: (
      <MainLayout title="Search Bar">
        <Suspense fallback={<Loading />}>
          <SearchBarPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/modern-menu',
    element: (
      <MainLayout title="Modern Menu">
        <Suspense fallback={<Loading />}>
          <ModernMenuPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/responsive-grid',
    element: (
      <MainLayout title="Responsive Grid">
        <Suspense fallback={<Loading />}>
          <ResponsiveGridPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/loading-button-demo',
    element: (
      <MainLayout title="Loading Button Demo">
        <Suspense fallback={<Loading />}>
          <LoadingButtonDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/theme',
    element: (
      <MainLayout title="Form Components with Theme System">
        <Suspense fallback={<Loading />}>
          <ThemeFormDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/avatar',
    element: (
      <MainLayout title="Form Components with Avatar System">
        <Suspense fallback={<Loading />}>
          <AvatarPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/banner',
    element: (
      <MainLayout title="Banner Component">
        <Suspense fallback={<Loading />}>
          <BannerPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/table',
    element: (
      <MainLayout title="Table">
        <Suspense fallback={<Loading />}>
          <TablePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/image-gallery',
    element: (
      <MainLayout title="ImageGalleryPage">
        <Suspense fallback={<Loading />}>
          <ImageGalleryPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/line-chart',
    element: (
      <MainLayout title="Line Chart">
        <Suspense fallback={<Loading />}>
          <LineChartPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/wizard',
    element: (
      <MainLayout title="Form Wizard">
        <Suspense fallback={<Loading />}>
          <FormWizardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/simple-chart',
    element: (
      <MainLayout title="Simple Chart">
        <Suspense fallback={<Loading />}>
          <SimpleChartPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/notification',
    element: (
      <MainLayout title="Notification">
        <Suspense fallback={<Loading />}>
          <NotificationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/flow-nodes',
    element: (
      <MainLayout title="Flow Nodes & Edges">
        <Suspense fallback={<Loading />}>
          <FlowNodesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/top-card',
    element: (
      <MainLayout title="TopCard">
        <Suspense fallback={<Loading />}>
          <TopCardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/data-display',
    element: (
      <MainLayout title="Data Display">
        <Suspense fallback={<Loading />}>
          <DataDisplayPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/card-components',
    element: (
      <MainLayout title="Card Components">
        <Suspense fallback={<Loading />}>
          <CardComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/tag-form',
    element: (
      <MainLayout title="Tag Form">
        <Suspense fallback={<Loading />}>
          <TagForm />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/search-input-with-image',
    element: (
      <MainLayout title="Search Input With Image">
        <Suspense fallback={<Loading />}>
          <SearchInputWithImagePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/analytics-components',
    element: (
      <MainLayout title="Analytics Components">
        <Suspense fallback={<Loading />}>
          <AnalyticsComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/chat-components',
    element: (
      <MainLayout title="Chat Components">
        <Suspense fallback={<Loading />}>
          <ChatComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/marketing-components',
    element: (
      <MainLayout title="Marketing Components">
        <Suspense fallback={<Loading />}>
          <MarketingComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/ai-components',
    element: (
      <MainLayout title="AI Components">
        <Suspense fallback={<Loading />}>
          <AIComponentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/charts/demo',
    element: (
      <MainLayout title="Chart Components Demo">
        <Suspense fallback={<Loading />}>
          <ChartDemoPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/kanban',
    element: (
      <MainLayout title="Kanban Board">
        <Suspense fallback={<Loading />}>
          <KanbanBoardPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Product Components Routes
  {
    path: '/components/product',
    element: (
      <MainLayout title="Product Components">
        <Suspense fallback={<Loading />}>
          <ProductComponentsDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/product/pages',
    element: (
      <MainLayout title="Product Pages">
        <Suspense fallback={<Loading />}>
          <ProductPagesDemo />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default componentRoutes;
