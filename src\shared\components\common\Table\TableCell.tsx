import React from 'react';

import { TableCellProps } from './types';

/**
 * Component ô của bảng
 */
const TableCell: React.FC<TableCellProps> = ({
  children,
  className = '',
  style,
  align = 'left',
  colSpan,
  rowSpan,
  ...rest
}) => {
  // Xác định các lớp căn chỉnh
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  // K<PERSON>t hợp tất cả các lớp
  const cellClasses = ['px-4 py-3', alignClasses[align], className].join(' ');

  return (
    <td className={cellClasses} style={style} colSpan={colSpan} rowSpan={rowSpan} {...rest}>
      {children}
    </td>
  );
};

export default TableCell;
