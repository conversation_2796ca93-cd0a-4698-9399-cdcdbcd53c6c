/**
 * Enum trạng thái công việc
 */
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * Enum mức độ ưu tiên
 */
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Interface cho Task
 */
export interface TaskDto {
  id: number;
  title: string;
  description: string | null;
  assigneeId: number;
  status: TaskStatus | null;
  priority: TaskPriority | null;
  expectedStars: number | null;
  awardedStars: number | null;
  createdBy: number;
  createdAt: number | null;
  updatedAt: number | null;
  completedAt: number | null;
  categoryId: number | null;
  parentId: number | null;
}

/**
 * Interface cho TaskQueryDto
 */
export interface TaskQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  status?: TaskStatus;
  priority?: TaskPriority;
  assigneeId?: number;
  categoryId?: number;
  parentId?: number | null;
  startDate?: number;
  endDate?: number;
}

/**
 * Interface cho CreateTaskDto
 */
export interface CreateTaskDto {
  title: string;
  description?: string;
  assigneeId?: number;
  priority?: TaskPriority;
  expectedStars?: number;
  categoryId?: number;
  parentId?: number;
  keyResultIds?: number[];
}

/**
 * Interface cho UpdateTaskDto
 */
export interface UpdateTaskDto {
  title?: string;
  description?: string;
  assigneeId?: number;
  priority?: TaskPriority;
  expectedStars?: number;
  categoryId?: number;
  parentId?: number;
}

/**
 * Interface cho UpdateTaskStatusDto
 */
export interface UpdateTaskStatusDto {
  status: TaskStatus;
}
