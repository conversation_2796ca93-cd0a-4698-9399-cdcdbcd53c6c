import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { ConfigModule, ConfigService, ConfigType, DatabaseConfig } from '@/config';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { Logger } from '@nestjs/common';
import { OkrCycleStatus } from '../enum/okr-cycle-status.enum';

/**
 * Test kết nối database với entity OkrCycle
 */
describe('Database Connection with OkrCycle Entity', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  let okrCycleRepository: Repository<OkrCycle>;
  const logger = new Logger('DatabaseConnectionTest');

  beforeAll(async () => {
    // Tạo testing module với TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        // Import ConfigModule để lấy cấu hình database
        ConfigModule,
        // Import TypeOrmModule với cấu hình từ ConfigService
        TypeOrmModule.forRootAsync({
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const dbConfig = configService.getConfig<DatabaseConfig>(ConfigType.Database);
            logger.log(`Connecting to database: ${dbConfig.database} on ${dbConfig.host}:${dbConfig.port}`);
            
            return {
              type: 'postgres',
              host: dbConfig.host,
              port: dbConfig.port,
              username: dbConfig.username,
              password: dbConfig.password,
              database: dbConfig.database,
              entities: [OkrCycle],
              synchronize: false,
              ssl: {
                rejectUnauthorized: !dbConfig.ssl,
              },
            };
          },
        }),
        // Import TypeOrmModule.forFeature để sử dụng repository
        TypeOrmModule.forFeature([OkrCycle]),
      ],
    }).compile();

    // Lấy DataSource và Repository
    dataSource = module.get<DataSource>(DataSource);
    okrCycleRepository = module.get<Repository<OkrCycle>>(Repository<OkrCycle>);
  });

  afterAll(async () => {
    // Đóng kết nối sau khi test xong
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should connect to the database successfully', async () => {
    // Kiểm tra kết nối đã được thiết lập
    expect(dataSource.isInitialized).toBe(true);

    // Log thông tin kết nối
    const { host, port, username, database } = dataSource.options as any;
    logger.log(`Connected to database: ${database} on ${host}:${port} as ${username}`);

    // Thử thực hiện một truy vấn đơn giản
    try {
      const result = await dataSource.query('SELECT 1 as value');
      expect(result).toBeDefined();
      expect(result[0].value).toBe(1);
      logger.log('Successfully executed test query');
    } catch (error) {
      logger.error(`Failed to execute test query: ${error.message}`);
      throw error;
    }
  });

  it('should have OkrCycle entity registered', () => {
    // Kiểm tra entity OkrCycle đã được đăng ký
    const entities = dataSource.entityMetadatas;
    const okrCycleEntity = entities.find(entity => entity.name === 'OkrCycle');
    
    expect(okrCycleEntity).toBeDefined();
    expect(okrCycleEntity?.name).toBe('OkrCycle');
    expect(okrCycleEntity?.tableName).toBe('okr_cycles');
  });

  it('should be able to query OkrCycle entity', async () => {
    // Kiểm tra repository đã được khởi tạo
    expect(okrCycleRepository).toBeDefined();

    // Thử thực hiện một truy vấn đơn giản
    try {
      const queryBuilder = okrCycleRepository.createQueryBuilder('cycle');
      const count = await queryBuilder.getCount();
      
      // Chỉ kiểm tra xem truy vấn có thành công không, không quan tâm đến kết quả
      expect(count).toBeGreaterThanOrEqual(0);
      logger.log(`Found ${count} OkrCycle records`);
    } catch (error) {
      logger.error(`Failed to query OkrCycle: ${error.message}`);
      throw error;
    }
  });

  it('should be able to create and delete a test OkrCycle record', async () => {
    // Tạo một bản ghi test
    const testCycle = okrCycleRepository.create({
      name: 'Test Cycle',
      startDate: new Date('2025-01-01'),
      endDate: new Date('2025-03-31'),
      status: OkrCycleStatus.PLANNING,
      createdAt: Date.now(),
      tenantId: 999, // Sử dụng tenant ID test
    });

    try {
      // Lưu bản ghi test
      const savedCycle = await okrCycleRepository.save(testCycle);
      expect(savedCycle).toBeDefined();
      expect(savedCycle.id).toBeDefined();
      expect(savedCycle.name).toBe('Test Cycle');
      logger.log(`Created test OkrCycle with ID: ${savedCycle.id}`);

      // Xóa bản ghi test
      await okrCycleRepository.delete(savedCycle.id);
      
      // Kiểm tra xem bản ghi đã bị xóa chưa
      const deletedCycle = await okrCycleRepository.findOne({
        where: { id: savedCycle.id }
      });
      expect(deletedCycle).toBeNull();
      logger.log(`Successfully deleted test OkrCycle with ID: ${savedCycle.id}`);
    } catch (error) {
      logger.error(`Test failed: ${error.message}`);
      throw error;
    }
  });
});
