import { StrictMode } from 'react';

import '@/shared/styles/react-flow.css';
import '@/shared/styles/scrollbar.css';

import { createRoot } from 'react-dom/client';

import { queryClient } from '@/shared/api';
import { QueryClientProvider } from '@tanstack/react-query';

import 'reactflow/dist/style.css';

import App from './App.tsx';
import './index.css';

// Render ứng dụng
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </StrictMode>
);
